<?php
class Database {
    public $db;
    public $offline = false;
    public $error = null;

    public function __construct($server, $user, $pass, $db, $port) {
        try {
            $pdo = "sqlsrv:Server={$server},{$port};Database={$db}";
            $this->db = new PDO($pdo, $user, $pass);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            $this->offline = true;
            $this->error = $e->getMessage();
        }
    }

    public function query($sql, $params = []) {
        if (!is_array($params)) $params = [$params];
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($stmt->execute($params)) { $stmt->closeCursor(); return true; }
        return false;
    }

    public function queryFetch($sql, $params = []) {
        if (!is_array($params)) $params = [$params];
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($stmt->execute($params)) { $r = $stmt->fetchAll(PDO::FETCH_ASSOC); $stmt->closeCursor(); return $r ?: null; }
        return false;
    }

    public function queryFetchSingle($sql, $params = []) {
        $r = $this->queryFetch($sql, $params);
        return isset($r[0]) ? $r[0] : null;
    }
}

