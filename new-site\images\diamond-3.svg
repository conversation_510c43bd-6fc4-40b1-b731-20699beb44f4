<svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M29.293 19L19 29.293L8.70703 19L19 8.70703L29.293 19Z" stroke="#FFA500" stroke-opacity="0.09"/>
  <g filter="url(#filter0_di_124_360)">
    <g clip-path="url(#paint0_angular_124_360_clip_path)" data-figma-skip-parse="true">
      <g transform="matrix(0 -0.00572 0.00572 0 19 19)">
        <foreignObject x="-1174.83" y="-1174.83" width="2349.65" height="2349.65">
          <div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,#FF8C00 0deg,#FFA500 250.962deg,#FFD580 360deg);height:100%;width:100%;opacity:1"></div>
        </foreignObject>
      </g>
    </g>
    <path d="M19 13.28L24.72 19L19 24.72L13.28 19L19 13.28Z"/>
    <path d="M24.0124 18.9998L18.9997 24.0125L13.986 18.9998L18.9997 13.9861L24.0124 18.9998Z" stroke="#FFA500" stroke-opacity="0.09"/>
  </g>
  <defs>
    <filter id="filter0_di_124_360" x="0.279968" y="0.280029" width="37.44" height="37.4399" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="6.5"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.647 0 0 0 0 0 0 0 0 0.7 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_124_360"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_124_360" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="1"/>
      <feGaussianBlur stdDeviation="0.5"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.8 0 0 0 0 0.4 0 0 0 0.45 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_124_360"/>
    </filter>
    <clipPath id="paint0_angular_124_360_clip_path">
      <path d="M19 13.28L24.72 19L19 24.72L13.28 19L19 13.28Z"/>
    </clipPath>
  </defs>
</svg>
