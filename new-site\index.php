<?php
require_once __DIR__.'/app/auth.php';
start_session();

// Handle modal form posts coming from the landing page
if($_SERVER['REQUEST_METHOD']==='POST'){
    if(isset($_POST['login_submit'])){
        try{
            AuthService::login($_POST['account'] ?? '', $_POST['password'] ?? '');
            header('Location: usercp.php');
            exit;
        }catch(Exception $e){
            // Re-open login modal on error and pass a small error code + message for UI feedback
            $err = $e->getMessage();
            @file_put_contents(__DIR__.'/app/auth_debug.log', date('c')." web-login error: ".$err." user=".($_POST['account']??'')." ip=".($_SERVER['REMOTE_ADDR']??'')."\n", FILE_APPEND);
            $code = 'error';
            if (stripos($err,'offline')!==false) $code='db';
            elseif (stripos($err,'not found')!==false) $code='notfound';
            elseif (stripos($err,'Invalid password')!==false) $code='pwd';
            $emsg = rawurlencode(substr($err,0,140));
            header('Location: index.php?login=1&err='.$code.'&emsg='.$emsg);
            exit;
        }
    }
    if(isset($_POST['register_submit'])){
        try{
            AuthService::register($_POST['account'] ?? '', $_POST['email'] ?? '', $_POST['password'] ?? '');
            // After successful registration, send to login page
            header('Location: login.php');
            exit;
        }catch(Exception $e){
            // Re-open register modal on error if supported by template
            header('Location: index.php?joinUser=1&err=reg');
            exit;
        }
    }
}

// If already logged in, go straight to dashboard
if(isLoggedIn()){
    header('Location: usercp.php');
    exit;
}

// Render the original static template
readfile(__DIR__.'/index.html');

// If there is an error code, inject a small visible message into the login modal
if(isset($_GET['err'])){
    $map = [
        'db' => 'Database is offline or unreachable. Please check SQL Server/PDO drivers.',
        'notfound' => 'Account not found.',
        'pwd' => 'Invalid password.',
        'reg' => 'Registration failed. Please check your inputs.',
        'error' => 'Login failed.',
    ];
    $detail = isset($_GET['emsg']) ? urldecode($_GET['emsg']) : '';
    $msg = ($map[$_GET['err']] ?? 'Login failed.').($detail?" (".$detail.")":'');
    $safe = json_encode($msg);
    echo "<script>window.addEventListener('DOMContentLoaded',function(){try{var m=document.getElementById('modal-login');if(m){m.style.display='block';var f=m.querySelector('.popup-modal-content-register-form');if(f){var d=document.createElement('div');d.style='margin:10px 0;padding:10px;border:1px solid rgba(204,148,0,0.6);color:#ffce00;background:rgba(0,0,0,0.55);font:14px/1.4 sans-serif;';d.textContent={$safe};f.insertBefore(d,f.firstChild);}}}catch(e){}});</script>";
}
