/* Elden Aion Dashboard Theme (matches homepage look) */
:root{
  --ea-bg:#000; --ea-text:#e9e6dd; --ea-muted:#b3a995;
  --ea-border:rgba(204,184,109,.35); --ea-card-bg:rgba(5,5,5,.85);
  --ea-gold:#cf861c; --ea-gold-soft:rgba(204,184,109,.25);
  --elyos-blue:#4a90e2; --elyos-light:#6bb6ff;
  --asmo-red:#e74c3c; --asmo-light:#ff6b6b;
  --gradient-gold:linear-gradient(135deg, #ffd700, #ffb347, #ff8c00);
  --gradient-blue:linear-gradient(135deg, #4a90e2, #357abd, #2c5aa0);
  --gradient-red:linear-gradient(135deg, #e74c3c, #c0392b, #a93226);
}
html,body{height:100%}
body{background-color:#000;color:var(--ea-text);overflow-x:hidden}
.page-bg{position:fixed;inset:0;z-index:-1;pointer-events:none;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(204,184,109,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(74,144,226,0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(231,76,60,0.05) 0%, transparent 50%),
    url('../images/effect.png'),
    url('../images/bg_armor.png');
  background-repeat:no-repeat,no-repeat,no-repeat,no-repeat,no-repeat;
  background-position:top left, top right, center, top center, top center;
  background-size:100% 100%, 100% 100%, 100% 100%, cover, cover;
  filter:brightness(0.8);
  animation:backgroundPulse 20s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% { filter:brightness(0.8); }
  50% { filter:brightness(1.1); }
}
.container{max-width:1200px}
.navbar{background:linear-gradient(180deg,#1f2632,#171b24)}
/* Frame utility like homepage */
.frame{position:relative;background:var(--ea-card-bg);border:15px solid transparent;border-image:url('../images/frame_square_dark.png') 30 stretch}
.frame::before{content:'';position:absolute;inset:0;pointer-events:none;box-shadow:inset 0 0 0 1px var(--ea-gold-soft)}
/* Hero */
.hero{position:relative;border-radius:8px;padding:28px;background:linear-gradient(180deg,rgba(255,255,255,.06),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.1);overflow:hidden}
.hero:before{content:'';position:absolute;inset:-40px;opacity:.18;background:url('../img/aion-hero.jpg') center/cover no-repeat;filter:blur(6px)}
.hero h3{letter-spacing:.4px;font-family:'Cinzel',serif}
/* KPI cards */
.kpi .card{background:var(--ea-card-bg);border:1px solid var(--ea-border);color:var(--ea-text);transition:.25s transform,.25s box-shadow}
.kpi .card:hover{transform:translateY(-2px);box-shadow:0 10px 22px rgba(0,0,0,.45)}
/* Shop grid */
.shop-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.shop-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;box-shadow:0 8px 20px rgba(0,0,0,.35);overflow:hidden;transform:translateY(0);transition:transform .2s ease,box-shadow .2s ease}
.shop-card:hover{transform:translateY(-4px);box-shadow:0 12px 26px rgba(0,0,0,.45)}
.shop-card .thumb{height:160px;background:#0c0f14;display:flex;align-items:center;justify-content:center}
.shop-card img{max-width:100%;max-height:100%}
.shop-card .body{padding:12px;color:#c7d2e1}
.badge-credits{background:#b03315;color:#fff;border-radius:999px;padding:4px 10px;font-weight:600;font-size:.85rem}
.btn-aion{background:linear-gradient(90deg,#eabe43,#ff7a18);border:0;color:#1a1305;font-weight:700}
.footer-note{color:var(--ea-muted)}
/* Characters */
.char-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.char-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;padding:14px}
.char-card .name{font-weight:700}
.char-card .meta{color:var(--ea-muted);font-size:.9rem}
/* Server Statistics Styling */
.server-stats {
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.5);
}

.elyos-card {
  border-color: var(--elyos-blue);
  background: linear-gradient(135deg, rgba(74,144,226,0.1), var(--ea-card-bg));
}

.elyos-card:hover {
  box-shadow: 0 15px 35px rgba(74,144,226,0.3);
}

.asmo-card {
  border-color: var(--asmo-red);
  background: linear-gradient(135deg, rgba(231,76,60,0.1), var(--ea-card-bg));
}

.asmo-card:hover {
  box-shadow: 0 15px 35px rgba(231,76,60,0.3);
}

.total-card {
  border-color: var(--ea-gold);
  background: linear-gradient(135deg, rgba(204,184,109,0.1), var(--ea-card-bg));
}

.total-card:hover {
  box-shadow: 0 15px 35px rgba(204,184,109,0.3);
}

.level-card {
  border-color: #9b59b6;
  background: linear-gradient(135deg, rgba(155,89,182,0.1), var(--ea-card-bg));
}

.level-card:hover {
  box-shadow: 0 15px 35px rgba(155,89,182,0.3);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: block;
  filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
}

.stat-number {
  font-size: 2.2rem;
  font-weight: bold;
  color: var(--ea-text);
  margin-bottom: 0.25rem;
  transition: transform 0.2s ease;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--ea-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Top Players Section */
.top-players {
  margin-bottom: 2rem;
}

.ranking-card {
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  padding: 1.5rem;
  height: 100%;
  backdrop-filter: blur(10px);
}

.ranking-title {
  color: var(--ea-gold);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid var(--ea-border);
  padding-bottom: 0.5rem;
}

.player-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.player-rank-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.player-rank-item:hover {
  background: rgba(255,255,255,0.05);
  border-color: var(--ea-gold);
  transform: translateX(5px);
}

.rank-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--ea-gold);
  margin-right: 1rem;
  min-width: 2rem;
}

.player-info {
  flex: 1;
}

.player-name {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.player-name.elyos {
  color: var(--elyos-light);
}

.player-name.asmodian {
  color: var(--asmo-light);
}

.player-details {
  font-size: 0.85rem;
  color: var(--ea-muted);
}

.loading-placeholder {
  text-align: center;
  color: var(--ea-muted);
  font-style: italic;
  padding: 2rem;
}

/* Enhanced Animations */
@keyframes fadeUp{from{opacity:0;transform:translateY(8px)}to{opacity:1;transform:none}}
@keyframes slideInLeft{from{opacity:0;transform:translateX(-20px)}to{opacity:1;transform:translateX(0)}}
@keyframes slideInRight{from{opacity:0;transform:translateX(20px)}to{opacity:1;transform:translateX(0)}}
@keyframes pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.05)}}

.hero,.kpi .card,.shop-card,.char-card{animation:fadeUp .5s ease both}
.stat-card{animation:fadeUp .6s ease both}
.stat-card:nth-child(1){animation-delay:.1s}
.stat-card:nth-child(2){animation-delay:.2s}
.stat-card:nth-child(3){animation-delay:.3s}
.stat-card:nth-child(4){animation-delay:.4s}

.ranking-card:first-child{animation:slideInLeft .7s ease both}
.ranking-card:last-child{animation:slideInRight .7s ease both}

.player-rank-item{animation:fadeUp .4s ease both}
.player-rank-item:nth-child(1){animation-delay:.1s}
.player-rank-item:nth-child(2){animation-delay:.2s}
.player-rank-item:nth-child(3){animation-delay:.3s}
.player-rank-item:nth-child(4){animation-delay:.4s}
.player-rank-item:nth-child(5){animation-delay:.5s}

/* Activity Section */
.activity-section {
  margin-bottom: 2rem;
}

.activity-card {
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  padding: 1.5rem;
  height: 100%;
  backdrop-filter: blur(10px);
}

.activity-title {
  color: var(--ea-gold);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--ea-border);
  padding-bottom: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255,255,255,0.05);
  border-color: var(--ea-gold);
  transform: translateX(5px);
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  min-width: 2rem;
  text-align: center;
}

.activity-icon.siege { filter: hue-rotate(200deg); }
.activity-icon.boss { filter: hue-rotate(0deg); }
.activity-icon.event { filter: hue-rotate(60deg); }

.activity-content {
  flex: 1;
}

.activity-text {
  font-weight: 500;
  color: var(--ea-text);
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.8rem;
  color: var(--ea-muted);
}

/* Server Status Grid */
.server-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.75rem;
  position: relative;
}

.status-indicator.online {
  background: #2ecc71;
  box-shadow: 0 0 10px rgba(46,204,113,0.5);
}

.status-indicator.warning {
  background: #f39c12;
  box-shadow: 0 0 10px rgba(243,156,18,0.5);
}

.status-indicator.offline {
  background: #e74c3c;
  box-shadow: 0 0 10px rgba(231,76,60,0.5);
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,0.8);
  animation: pulse 2s infinite;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 0.85rem;
  color: var(--ea-muted);
  margin-bottom: 0.25rem;
}

.status-value {
  font-weight: 600;
  color: var(--ea-text);
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 2rem;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  text-decoration: none;
  color: var(--ea-text);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(204,184,109,0.1), transparent);
  transition: left 0.5s ease;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(204,184,109,0.2);
  border-color: var(--ea-gold);
  color: var(--ea-text);
  text-decoration: none;
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
}

.action-label {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Footer */
.footer-note {
  color: var(--ea-muted);
  font-size: 0.9rem;
  padding: 1rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stat-card {
    margin-bottom: 1rem;
  }

  .server-status-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: 1rem;
  }

  .action-icon {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .hero {
    padding: 1.5rem;
  }

  .activity-card, .ranking-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 1.8rem;
  }

  .hero h3 {
    font-size: 1.5rem;
  }

  .ranking-title, .activity-title {
    font-size: 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .stat-card, .activity-card, .ranking-card, .action-card {
    border-color: rgba(204,184,109,.2);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .page-bg {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stat-card, .activity-card, .ranking-card, .action-card {
    border-width: 2px;
    border-color: var(--ea-gold);
  }

  .player-name.elyos {
    color: #00bfff;
  }

  .player-name.asmodian {
    color: #ff4500;
  }
}

