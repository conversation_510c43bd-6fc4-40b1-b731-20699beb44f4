/* Elden Aion Dashboard Theme (matches homepage look) */
:root{
  --ea-bg:#000; --ea-text:#e9e6dd; --ea-muted:#b3a995;
  --ea-border:rgba(204,184,109,.35); --ea-card-bg:rgba(5,5,5,.85);
  --ea-gold:#cf861c; --ea-gold-soft:rgba(204,184,109,.25);
}
html,body{height:100%}
body{background-color:#000;color:var(--ea-text)}
.page-bg{position:fixed;inset:0;z-index:-1;pointer-events:none;
  background-image: url('../images/effect.png'), url('../images/bg_armor.png');
  background-repeat:no-repeat,no-repeat; background-position:top center, top center;
  background-size:cover, cover; filter:brightness(1);
}
.container{max-width:1200px}
.navbar{background:linear-gradient(180deg,#1f2632,#171b24)}
/* Frame utility like homepage */
.frame{position:relative;background:var(--ea-card-bg);border:15px solid transparent;border-image:url('../images/frame_square_dark.png') 30 stretch}
.frame::before{content:'';position:absolute;inset:0;pointer-events:none;box-shadow:inset 0 0 0 1px var(--ea-gold-soft)}
/* Hero */
.hero{position:relative;border-radius:8px;padding:28px;background:linear-gradient(180deg,rgba(255,255,255,.06),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.1);overflow:hidden}
.hero:before{content:'';position:absolute;inset:-40px;opacity:.18;background:url('../img/aion-hero.jpg') center/cover no-repeat;filter:blur(6px)}
.hero h3{letter-spacing:.4px;font-family:'Cinzel',serif}
/* KPI cards */
.kpi .card{background:var(--ea-card-bg);border:1px solid var(--ea-border);color:var(--ea-text);transition:.25s transform,.25s box-shadow}
.kpi .card:hover{transform:translateY(-2px);box-shadow:0 10px 22px rgba(0,0,0,.45)}
/* Shop grid */
.shop-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.shop-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;box-shadow:0 8px 20px rgba(0,0,0,.35);overflow:hidden;transform:translateY(0);transition:transform .2s ease,box-shadow .2s ease}
.shop-card:hover{transform:translateY(-4px);box-shadow:0 12px 26px rgba(0,0,0,.45)}
.shop-card .thumb{height:160px;background:#0c0f14;display:flex;align-items:center;justify-content:center}
.shop-card img{max-width:100%;max-height:100%}
.shop-card .body{padding:12px;color:#c7d2e1}
.badge-credits{background:#b03315;color:#fff;border-radius:999px;padding:4px 10px;font-weight:600;font-size:.85rem}
.btn-aion{background:linear-gradient(90deg,#eabe43,#ff7a18);border:0;color:#1a1305;font-weight:700}
.footer-note{color:var(--ea-muted)}
/* Characters */
.char-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.char-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;padding:14px}
.char-card .name{font-weight:700}
.char-card .meta{color:var(--ea-muted);font-size:.9rem}
/* Subtle appear animation */
@keyframes fadeUp{from{opacity:0;transform:translateY(8px)}to{opacity:1;transform:none}}
.hero,.kpi .card,.shop-card,.char-card{animation:fadeUp .5s ease both}

