/* Elden Aion Dashboard Theme (matches homepage look) */
:root{
  --ea-bg:#000; --ea-text:#e9e6dd; --ea-muted:#b3a995;
  --ea-border:rgba(204,184,109,.35); --ea-card-bg:rgba(5,5,5,.85);
  --ea-gold:#cf861c; --ea-gold-soft:rgba(204,184,109,.25);
  --elyos-blue:#4a90e2; --elyos-light:#6bb6ff;
  --asmo-red:#e74c3c; --asmo-light:#ff6b6b;
  --gradient-gold:linear-gradient(135deg, #ffd700, #ffb347, #ff8c00);
  --gradient-blue:linear-gradient(135deg, #4a90e2, #357abd, #2c5aa0);
  --gradient-red:linear-gradient(135deg, #e74c3c, #c0392b, #a93226);
}
html,body{height:100%}
body{background-color:#000;color:var(--ea-text);overflow-x:hidden;position:relative}

/* Enhanced Aion-themed Background */
.page-bg{position:fixed;inset:0;z-index:-1;pointer-events:none;
  background:
    radial-gradient(circle at 20% 80%, rgba(204,184,109,0.15) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(74,144,226,0.1) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(231,76,60,0.08) 0%, transparent 60%),
    linear-gradient(45deg, rgba(0,0,0,0.9) 0%, rgba(20,20,40,0.8) 50%, rgba(0,0,0,0.9) 100%),
    url('../images/bg_atreia.jpg');
  background-repeat:no-repeat,no-repeat,no-repeat,no-repeat,no-repeat;
  background-position:top left, top right, center, center, center;
  background-size:100% 100%, 100% 100%, 100% 100%, cover, cover;
  filter:brightness(0.6) contrast(1.2) saturate(1.1);
  animation:backgroundPulse 25s ease-in-out infinite;
}

/* Floating Particles Effect */
.page-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255,215,0,0.8), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(74,144,226,0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(231,76,60,0.5), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.4), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(204,184,109,0.7), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: floatingParticles 30s linear infinite;
  opacity: 0.6;
}

/* Magical Aura Effect */
.page-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    conic-gradient(from 0deg at 50% 50%,
      transparent 0deg,
      rgba(255,215,0,0.1) 60deg,
      transparent 120deg,
      rgba(74,144,226,0.08) 180deg,
      transparent 240deg,
      rgba(231,76,60,0.06) 300deg,
      transparent 360deg);
  animation: rotatingAura 40s linear infinite;
  opacity: 0.3;
}

@keyframes backgroundPulse {
  0%, 100% {
    filter:brightness(0.6) contrast(1.2) saturate(1.1);
    transform: scale(1);
  }
  25% {
    filter:brightness(0.8) contrast(1.3) saturate(1.2);
    transform: scale(1.02);
  }
  50% {
    filter:brightness(1.0) contrast(1.4) saturate(1.3);
    transform: scale(1.01);
  }
  75% {
    filter:brightness(0.7) contrast(1.25) saturate(1.15);
    transform: scale(1.015);
  }
}

@keyframes floatingParticles {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-20px) translateX(10px); }
  50% { transform: translateY(-40px) translateX(-5px); }
  75% { transform: translateY(-20px) translateX(-10px); }
  100% { transform: translateY(0px) translateX(0px); }
}

@keyframes rotatingAura {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* Floating Magical Particles */
.magic-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.7;
  filter: blur(1px);
}

.particle-1 {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255,215,0,0.8), rgba(255,215,0,0.2));
  top: 20%;
  left: 10%;
  animation: floatParticle1 25s linear infinite;
  box-shadow: 0 0 10px rgba(255,215,0,0.5);
}

.particle-2 {
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, rgba(74,144,226,0.8), rgba(74,144,226,0.2));
  top: 60%;
  left: 20%;
  animation: floatParticle2 30s linear infinite;
  box-shadow: 0 0 12px rgba(74,144,226,0.5);
}

.particle-3 {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(231,76,60,0.8), rgba(231,76,60,0.2));
  top: 40%;
  left: 80%;
  animation: floatParticle3 20s linear infinite;
  box-shadow: 0 0 8px rgba(231,76,60,0.5);
}

.particle-4 {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(155,89,182,0.8), rgba(155,89,182,0.2));
  top: 80%;
  left: 70%;
  animation: floatParticle4 35s linear infinite;
  box-shadow: 0 0 10px rgba(155,89,182,0.5);
}

.particle-5 {
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, rgba(255,255,255,0.9), rgba(255,255,255,0.3));
  top: 30%;
  left: 50%;
  animation: floatParticle5 18s linear infinite;
  box-shadow: 0 0 6px rgba(255,255,255,0.6);
}

.particle-6 {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255,215,0,0.7), rgba(255,215,0,0.1));
  top: 70%;
  left: 30%;
  animation: floatParticle6 28s linear infinite;
  box-shadow: 0 0 8px rgba(255,215,0,0.4);
}

.particle-7 {
  width: 3px;
  height: 3px;
  background: radial-gradient(circle, rgba(74,144,226,0.6), rgba(74,144,226,0.1));
  top: 15%;
  left: 90%;
  animation: floatParticle7 22s linear infinite;
  box-shadow: 0 0 6px rgba(74,144,226,0.4);
}

.particle-8 {
  width: 5px;
  height: 5px;
  background: radial-gradient(circle, rgba(231,76,60,0.7), rgba(231,76,60,0.1));
  top: 90%;
  left: 15%;
  animation: floatParticle8 32s linear infinite;
  box-shadow: 0 0 10px rgba(231,76,60,0.4);
}

@keyframes floatParticle1 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.7; }
  90% { opacity: 0.7; }
  100% { transform: translateY(-100vh) translateX(50px) rotate(360deg); opacity: 0; }
}

@keyframes floatParticle2 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.8; }
  90% { opacity: 0.8; }
  100% { transform: translateY(-100vh) translateX(-30px) rotate(-360deg); opacity: 0; }
}

@keyframes floatParticle3 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.6; }
  90% { opacity: 0.6; }
  100% { transform: translateY(-100vh) translateX(-80px) rotate(360deg); opacity: 0; }
}

@keyframes floatParticle4 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.7; }
  90% { opacity: 0.7; }
  100% { transform: translateY(-100vh) translateX(40px) rotate(-360deg); opacity: 0; }
}

@keyframes floatParticle5 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.9; }
  90% { opacity: 0.9; }
  100% { transform: translateY(-100vh) translateX(-20px) rotate(360deg); opacity: 0; }
}

@keyframes floatParticle6 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.7; }
  90% { opacity: 0.7; }
  100% { transform: translateY(-100vh) translateX(60px) rotate(-360deg); opacity: 0; }
}

@keyframes floatParticle7 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.6; }
  90% { opacity: 0.6; }
  100% { transform: translateY(-100vh) translateX(-70px) rotate(360deg); opacity: 0; }
}

@keyframes floatParticle8 {
  0% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 0.7; }
  90% { opacity: 0.7; }
  100% { transform: translateY(-100vh) translateX(35px) rotate(-360deg); opacity: 0; }
}
.container{max-width:1200px}
.navbar{background:linear-gradient(180deg,#1f2632,#171b24)}
/* Frame utility like homepage */
.frame{position:relative;background:var(--ea-card-bg);border:15px solid transparent;border-image:url('../images/frame_square_dark.png') 30 stretch}
.frame::before{content:'';position:absolute;inset:0;pointer-events:none;box-shadow:inset 0 0 0 1px var(--ea-gold-soft)}
/* Enhanced Hero Section */
.hero{
  position:relative;
  border-radius:20px;
  padding:2rem;
  background:
    linear-gradient(145deg, rgba(255,255,255,.08), rgba(255,255,255,.02)),
    radial-gradient(circle at 30% 30%, rgba(255,215,0,0.1), transparent 60%),
    radial-gradient(circle at 70% 70%, rgba(74,144,226,0.05), transparent 60%);
  border:2px solid rgba(255,215,0,.3);
  overflow:hidden;
  backdrop-filter:blur(15px);
  box-shadow:
    0 15px 35px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.1),
    0 0 50px rgba(255,215,0,0.1);
  transition: all 0.4s ease;
}

.hero::before{
  content:'';
  position:absolute;
  inset:-50px;
  opacity:.15;
  background:
    url('../images/bg_atreia.jpg') center/cover no-repeat;
  filter:blur(8px) brightness(0.7) contrast(1.2);
  animation: heroBackgroundShift 20s ease-in-out infinite;
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255,215,0,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(74,144,226,0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(231,76,60,0.06) 0%, transparent 50%);
  animation: heroAura 15s ease-in-out infinite;
  pointer-events: none;
}

.hero:hover {
  border-color: rgba(255,215,0,.6);
  box-shadow:
    0 20px 40px rgba(0,0,0,0.5),
    inset 0 1px 0 rgba(255,255,255,0.2),
    0 0 60px rgba(255,215,0,0.2);
  transform: translateY(-2px);
}

.hero h3{
  letter-spacing:1px;
  font-family:'Cinzel',serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--ea-text);
  text-shadow:
    0 0 20px rgba(255,215,0,0.5),
    0 2px 4px rgba(0,0,0,0.7);
  position: relative;
  z-index: 2;
  animation: heroTitleGlow 4s ease-in-out infinite;
}

@keyframes heroBackgroundShift {
  0%, 100% {
    transform: scale(1.1) rotate(0deg);
    filter: blur(8px) brightness(0.7) contrast(1.2);
  }
  25% {
    transform: scale(1.15) rotate(1deg);
    filter: blur(10px) brightness(0.8) contrast(1.3);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
    filter: blur(12px) brightness(0.9) contrast(1.4);
  }
  75% {
    transform: scale(1.15) rotate(-1deg);
    filter: blur(10px) brightness(0.8) contrast(1.3);
  }
}

@keyframes heroAura {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  33% { opacity: 0.8; transform: scale(1.05); }
  66% { opacity: 0.7; transform: scale(0.98); }
}

@keyframes heroTitleGlow {
  0%, 100% {
    text-shadow:
      0 0 20px rgba(255,215,0,0.5),
      0 2px 4px rgba(0,0,0,0.7);
  }
  50% {
    text-shadow:
      0 0 30px rgba(255,215,0,0.8),
      0 0 50px rgba(255,215,0,0.4),
      0 2px 4px rgba(0,0,0,0.7);
  }
}
/* KPI cards */
.kpi .card{background:var(--ea-card-bg);border:1px solid var(--ea-border);color:var(--ea-text);transition:.25s transform,.25s box-shadow}
.kpi .card:hover{transform:translateY(-2px);box-shadow:0 10px 22px rgba(0,0,0,.45)}
/* Shop grid */
.shop-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.shop-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;box-shadow:0 8px 20px rgba(0,0,0,.35);overflow:hidden;transform:translateY(0);transition:transform .2s ease,box-shadow .2s ease}
.shop-card:hover{transform:translateY(-4px);box-shadow:0 12px 26px rgba(0,0,0,.45)}
.shop-card .thumb{height:160px;background:#0c0f14;display:flex;align-items:center;justify-content:center}
.shop-card img{max-width:100%;max-height:100%}
.shop-card .body{padding:12px;color:#c7d2e1}
.badge-credits{background:#b03315;color:#fff;border-radius:999px;padding:4px 10px;font-weight:600;font-size:.85rem}
.btn-aion{
  background:linear-gradient(135deg,#eabe43,#ff7a18,#ffd700);
  border:0;
  color:#1a1305;
  font-weight:700;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  box-shadow:
    0 4px 15px rgba(234,190,67,0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

.btn-aion::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.btn-aion:hover::before {
  left: 100%;
}

.btn-aion:hover {
  background:linear-gradient(135deg,#ffd700,#eabe43,#ff7a18);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(234,190,67,0.6),
    inset 0 1px 0 rgba(255,255,255,0.4);
  color:#1a1305;
}
.footer-note{color:var(--ea-muted)}
/* Characters */
.char-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(220px,1fr));gap:16px}
.char-card{background:var(--ea-card-bg);border:1px solid var(--ea-border);border-radius:12px;padding:14px}
.char-card .name{font-weight:700}
.char-card .meta{color:var(--ea-muted);font-size:.9rem}
/* Server Statistics Styling */
.server-stats {
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(145deg, rgba(5,5,5,0.95), rgba(20,20,40,0.9));
  border: 2px solid var(--ea-border);
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 32px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.1),
    inset 0 -1px 0 rgba(0,0,0,0.2);
}

/* Magical shimmer effect */
.stat-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255,215,0,0.2), transparent, rgba(74,144,226,0.15), transparent);
  animation: magicalShimmer 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Glowing border effect */
.stat-card::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 20px;
  padding: 2px;
  background: linear-gradient(45deg, transparent, rgba(255,215,0,0.3), transparent, rgba(74,144,226,0.2), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover::after {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0,0,0,0.6),
    0 0 30px rgba(255,215,0,0.2),
    inset 0 1px 0 rgba(255,255,255,0.2);
  border-color: rgba(255,215,0,0.5);
}

@keyframes magicalShimmer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elyos-card {
  border-color: var(--elyos-blue);
  background:
    linear-gradient(145deg, rgba(74,144,226,0.15), rgba(5,5,5,0.95)),
    radial-gradient(circle at 30% 30%, rgba(74,144,226,0.2), transparent 70%);
  position: relative;
}

.elyos-card::before {
  background: conic-gradient(from 0deg, transparent, rgba(74,144,226,0.4), transparent, rgba(135,206,250,0.3), transparent);
}

.elyos-card:hover {
  box-shadow:
    0 20px 40px rgba(74,144,226,0.4),
    0 0 50px rgba(74,144,226,0.3),
    inset 0 1px 0 rgba(135,206,250,0.3);
  border-color: rgba(74,144,226,0.8);
}

.asmo-card {
  border-color: var(--asmo-red);
  background:
    linear-gradient(145deg, rgba(231,76,60,0.15), rgba(5,5,5,0.95)),
    radial-gradient(circle at 70% 30%, rgba(231,76,60,0.2), transparent 70%);
}

.asmo-card::before {
  background: conic-gradient(from 0deg, transparent, rgba(231,76,60,0.4), transparent, rgba(255,99,71,0.3), transparent);
}

.asmo-card:hover {
  box-shadow:
    0 20px 40px rgba(231,76,60,0.4),
    0 0 50px rgba(231,76,60,0.3),
    inset 0 1px 0 rgba(255,99,71,0.3);
  border-color: rgba(231,76,60,0.8);
}

.total-card {
  border-color: var(--ea-gold);
  background:
    linear-gradient(145deg, rgba(255,215,0,0.15), rgba(5,5,5,0.95)),
    radial-gradient(circle at 50% 50%, rgba(255,215,0,0.2), transparent 70%);
}

.total-card::before {
  background: conic-gradient(from 0deg, transparent, rgba(255,215,0,0.5), transparent, rgba(255,223,0,0.4), transparent);
}

.total-card:hover {
  box-shadow:
    0 20px 40px rgba(255,215,0,0.4),
    0 0 50px rgba(255,215,0,0.3),
    inset 0 1px 0 rgba(255,223,0,0.3);
  border-color: rgba(255,215,0,0.8);
}

.level-card {
  border-color: #9b59b6;
  background:
    linear-gradient(145deg, rgba(155,89,182,0.15), rgba(5,5,5,0.95)),
    radial-gradient(circle at 30% 70%, rgba(155,89,182,0.2), transparent 70%);
}

.level-card::before {
  background: conic-gradient(from 0deg, transparent, rgba(155,89,182,0.4), transparent, rgba(186,85,211,0.3), transparent);
}

.level-card:hover {
  box-shadow:
    0 20px 40px rgba(155,89,182,0.4),
    0 0 50px rgba(155,89,182,0.3),
    inset 0 1px 0 rgba(186,85,211,0.3);
  border-color: rgba(155,89,182,0.8);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  display: block;
  filter:
    drop-shadow(0 0 15px rgba(255,255,255,0.4))
    drop-shadow(0 0 30px rgba(255,215,0,0.3));
  animation: iconFloat 3s ease-in-out infinite;
  position: relative;
}

.stat-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,215,0,0.1), transparent 70%);
  animation: iconGlow 2s ease-in-out infinite alternate;
  z-index: -1;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--ea-text);
  margin-bottom: 0.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow:
    0 0 10px rgba(255,255,255,0.3),
    0 0 20px rgba(255,215,0,0.2),
    0 2px 4px rgba(0,0,0,0.5);
  position: relative;
  animation: numberPulse 4s ease-in-out infinite;
}

.stat-number::before {
  content: attr(data-value);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255,215,0,0.3), transparent);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShine 3s linear infinite;
}

.stat-label {
  font-size: 0.95rem;
  color: var(--ea-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  animation: labelGlow 4s ease-in-out infinite;
}

/* Faction-specific icon effects */
.elyos-card .stat-icon {
  filter:
    drop-shadow(0 0 15px rgba(74,144,226,0.6))
    drop-shadow(0 0 30px rgba(135,206,250,0.4));
}

.elyos-card .stat-number {
  text-shadow:
    0 0 10px rgba(74,144,226,0.5),
    0 0 20px rgba(135,206,250,0.3),
    0 2px 4px rgba(0,0,0,0.5);
}

.asmo-card .stat-icon {
  filter:
    drop-shadow(0 0 15px rgba(231,76,60,0.6))
    drop-shadow(0 0 30px rgba(255,99,71,0.4));
}

.asmo-card .stat-number {
  text-shadow:
    0 0 10px rgba(231,76,60,0.5),
    0 0 20px rgba(255,99,71,0.3),
    0 2px 4px rgba(0,0,0,0.5);
}

.level-card .stat-icon {
  filter:
    drop-shadow(0 0 15px rgba(155,89,182,0.6))
    drop-shadow(0 0 30px rgba(186,85,211,0.4));
}

.level-card .stat-number {
  text-shadow:
    0 0 10px rgba(155,89,182,0.5),
    0 0 20px rgba(186,85,211,0.3),
    0 2px 4px rgba(0,0,0,0.5);
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-5px) rotate(2deg); }
  50% { transform: translateY(-8px) rotate(0deg); }
  75% { transform: translateY(-3px) rotate(-2deg); }
}

@keyframes iconGlow {
  0% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes numberPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes textShine {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes labelGlow {
  0%, 100% { text-shadow: 0 1px 2px rgba(0,0,0,0.5); }
  50% { text-shadow: 0 1px 2px rgba(0,0,0,0.5), 0 0 10px rgba(255,215,0,0.3); }
}

/* Top Players Section */
.top-players {
  margin-bottom: 2rem;
}

.ranking-card {
  background:
    linear-gradient(145deg, rgba(5,5,5,0.95), rgba(20,20,40,0.9)),
    radial-gradient(circle at 50% 0%, rgba(255,215,0,0.1), transparent 60%);
  border: 2px solid var(--ea-border);
  border-radius: 20px;
  padding: 1.5rem;
  height: 100%;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 32px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.1);
}

.ranking-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255,215,0,0.15), transparent);
  animation: cardShimmer 6s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ranking-card:hover::before {
  opacity: 1;
}

.ranking-card:hover {
  transform: translateY(-5px) scale(1.02);
  border-color: rgba(255,215,0,0.6);
  box-shadow:
    0 15px 40px rgba(0,0,0,0.6),
    0 0 30px rgba(255,215,0,0.2),
    inset 0 1px 0 rgba(255,255,255,0.2);
}

.ranking-title {
  color: var(--ea-gold);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
  border-bottom: 2px solid var(--ea-border);
  padding-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow:
    0 0 10px rgba(255,215,0,0.5),
    0 2px 4px rgba(0,0,0,0.5);
  position: relative;
  animation: titleGlow 3s ease-in-out infinite;
}

.ranking-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--ea-gold), transparent);
  animation: titleUnderline 2s ease-in-out infinite;
}

@keyframes cardShimmer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow:
      0 0 10px rgba(255,215,0,0.5),
      0 2px 4px rgba(0,0,0,0.5);
  }
  50% {
    text-shadow:
      0 0 20px rgba(255,215,0,0.8),
      0 0 30px rgba(255,215,0,0.4),
      0 2px 4px rgba(0,0,0,0.5);
  }
}

@keyframes titleUnderline {
  0%, 100% { width: 50px; opacity: 0.7; }
  50% { width: 80px; opacity: 1; }
}

.player-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.player-rank-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background:
    linear-gradient(135deg, rgba(255,255,255,0.03), rgba(255,255,255,0.01)),
    radial-gradient(circle at 10% 50%, rgba(255,215,0,0.05), transparent 60%);
  border: 1px solid rgba(255,255,255,0.08);
  border-radius: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.player-rank-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,215,0,0.1), transparent);
  transition: left 0.6s ease;
}

.player-rank-item:hover::before {
  left: 100%;
}

.player-rank-item:hover {
  background:
    linear-gradient(135deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03)),
    radial-gradient(circle at 10% 50%, rgba(255,215,0,0.15), transparent 60%);
  border-color: var(--ea-gold);
  transform: translateX(8px) translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0,0,0,0.3),
    0 0 20px rgba(255,215,0,0.2);
}

.rank-number {
  font-size: 1.4rem;
  font-weight: 900;
  color: var(--ea-gold);
  margin-right: 1.25rem;
  min-width: 2.5rem;
  text-align: center;
  text-shadow:
    0 0 10px rgba(255,215,0,0.6),
    0 2px 4px rgba(0,0,0,0.5);
  position: relative;
  animation: rankGlow 3s ease-in-out infinite;
}

.rank-number::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255,215,0,0.2), transparent 70%);
  animation: rankPulse 2s ease-in-out infinite;
  z-index: -1;
}

.player-info {
  flex: 1;
}

.player-name {
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  position: relative;
  animation: nameShimmer 4s ease-in-out infinite;
}

.player-name.elyos {
  color: var(--elyos-light);
  text-shadow:
    0 0 10px rgba(74,144,226,0.6),
    0 1px 2px rgba(0,0,0,0.5);
}

.player-name.asmodian {
  color: var(--asmo-light);
  text-shadow:
    0 0 10px rgba(231,76,60,0.6),
    0 1px 2px rgba(0,0,0,0.5);
}

.player-details {
  font-size: 0.9rem;
  color: var(--ea-muted);
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0,0,0,0.3);
}

/* Special effects for top 3 players */
.player-rank-item:nth-child(1) .rank-number {
  color: #ffd700;
  text-shadow:
    0 0 15px rgba(255,215,0,0.8),
    0 0 30px rgba(255,215,0,0.4),
    0 2px 4px rgba(0,0,0,0.5);
  animation: goldGlow 2s ease-in-out infinite;
}

.player-rank-item:nth-child(2) .rank-number {
  color: #c0c0c0;
  text-shadow:
    0 0 15px rgba(192,192,192,0.8),
    0 2px 4px rgba(0,0,0,0.5);
}

.player-rank-item:nth-child(3) .rank-number {
  color: #cd7f32;
  text-shadow:
    0 0 15px rgba(205,127,50,0.8),
    0 2px 4px rgba(0,0,0,0.5);
}

@keyframes rankGlow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes rankPulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes nameShimmer {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes goldGlow {
  0%, 100% {
    text-shadow:
      0 0 15px rgba(255,215,0,0.8),
      0 0 30px rgba(255,215,0,0.4),
      0 2px 4px rgba(0,0,0,0.5);
  }
  50% {
    text-shadow:
      0 0 25px rgba(255,215,0,1),
      0 0 50px rgba(255,215,0,0.6),
      0 2px 4px rgba(0,0,0,0.5);
  }
}

.loading-placeholder {
  text-align: center;
  color: var(--ea-muted);
  font-style: italic;
  padding: 2rem;
  position: relative;
  animation: loadingPulse 2s ease-in-out infinite;
}

.loading-placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  border: 3px solid transparent;
  border-top: 3px solid var(--ea-gold);
  border-radius: 50%;
  animation: loadingSpinner 1s linear infinite;
}

.loading-placeholder::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-bottom: 2px solid var(--elyos-blue);
  border-radius: 50%;
  animation: loadingSpinner 1.5s linear infinite reverse;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes loadingSpinner {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Enhanced Section Titles */
.section-title {
  position: relative;
  display: inline-block;
  font-size: 1.4rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  color: var(--ea-text);
  text-shadow:
    0 0 20px rgba(255,215,0,0.6),
    0 2px 4px rgba(0,0,0,0.8);
  animation: titlePulse 4s ease-in-out infinite;
}

.title-underline {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--ea-gold) 20%,
    var(--elyos-blue) 50%,
    var(--asmo-red) 80%,
    transparent 100%);
  border-radius: 2px;
  animation: underlineFlow 3s ease-in-out infinite;
}

.icon-glow {
  filter:
    drop-shadow(0 0 10px rgba(255,215,0,0.8))
    drop-shadow(0 0 20px rgba(255,215,0,0.4));
  animation: iconBreathe 3s ease-in-out infinite;
}

/* Magical Cursor Trail Effect */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(255,215,0,0.6), transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Scroll-triggered animations */
[data-aos="fade-up"] {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-aos="fade-up"].aos-animate {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced hover effects for interactive elements */
.stat-card, .ranking-card, .activity-card, .action-card {
  cursor: pointer;
  user-select: none;
}

.stat-card:active, .ranking-card:active, .activity-card:active, .action-card:active {
  transform: scale(0.98);
}

/* Magical sparkle effect on hover */
.stat-card:hover::after, .ranking-card:hover::after {
  content: '✨';
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.2rem;
  animation: sparkle 0.6s ease-out;
  pointer-events: none;
}

@keyframes titlePulse {
  0%, 100% {
    text-shadow:
      0 0 20px rgba(255,215,0,0.6),
      0 2px 4px rgba(0,0,0,0.8);
  }
  50% {
    text-shadow:
      0 0 30px rgba(255,215,0,0.9),
      0 0 50px rgba(255,215,0,0.5),
      0 2px 4px rgba(0,0,0,0.8);
  }
}

@keyframes underlineFlow {
  0% {
    background: linear-gradient(90deg,
      transparent 0%,
      var(--ea-gold) 20%,
      var(--elyos-blue) 50%,
      var(--asmo-red) 80%,
      transparent 100%);
  }
  33% {
    background: linear-gradient(90deg,
      transparent 0%,
      var(--elyos-blue) 20%,
      var(--asmo-red) 50%,
      var(--ea-gold) 80%,
      transparent 100%);
  }
  66% {
    background: linear-gradient(90deg,
      transparent 0%,
      var(--asmo-red) 20%,
      var(--ea-gold) 50%,
      var(--elyos-blue) 80%,
      transparent 100%);
  }
  100% {
    background: linear-gradient(90deg,
      transparent 0%,
      var(--ea-gold) 20%,
      var(--elyos-blue) 50%,
      var(--asmo-red) 80%,
      transparent 100%);
  }
}

@keyframes iconBreathe {
  0%, 100% {
    filter:
      drop-shadow(0 0 10px rgba(255,215,0,0.8))
      drop-shadow(0 0 20px rgba(255,215,0,0.4));
    transform: scale(1);
  }
  50% {
    filter:
      drop-shadow(0 0 15px rgba(255,215,0,1))
      drop-shadow(0 0 30px rgba(255,215,0,0.6));
    transform: scale(1.1);
  }
}

@keyframes sparkle {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(360deg);
  }
}

/* Enhanced Animations */
@keyframes fadeUp{from{opacity:0;transform:translateY(8px)}to{opacity:1;transform:none}}
@keyframes slideInLeft{from{opacity:0;transform:translateX(-20px)}to{opacity:1;transform:translateX(0)}}
@keyframes slideInRight{from{opacity:0;transform:translateX(20px)}to{opacity:1;transform:translateX(0)}}
@keyframes pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.05)}}

.hero,.kpi .card,.shop-card,.char-card{animation:fadeUp .5s ease both}
.stat-card{animation:fadeUp .6s ease both}
.stat-card:nth-child(1){animation-delay:.1s}
.stat-card:nth-child(2){animation-delay:.2s}
.stat-card:nth-child(3){animation-delay:.3s}
.stat-card:nth-child(4){animation-delay:.4s}

.ranking-card:first-child{animation:slideInLeft .7s ease both}
.ranking-card:last-child{animation:slideInRight .7s ease both}

.player-rank-item{animation:fadeUp .4s ease both}
.player-rank-item:nth-child(1){animation-delay:.1s}
.player-rank-item:nth-child(2){animation-delay:.2s}
.player-rank-item:nth-child(3){animation-delay:.3s}
.player-rank-item:nth-child(4){animation-delay:.4s}
.player-rank-item:nth-child(5){animation-delay:.5s}

/* Activity Section */
.activity-section {
  margin-bottom: 2rem;
}

.activity-card {
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  padding: 1.5rem;
  height: 100%;
  backdrop-filter: blur(10px);
}

.activity-title {
  color: var(--ea-gold);
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--ea-border);
  padding-bottom: 0.5rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255,255,255,0.05);
  border-color: var(--ea-gold);
  transform: translateX(5px);
}

.activity-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  min-width: 2rem;
  text-align: center;
}

.activity-icon.siege { filter: hue-rotate(200deg); }
.activity-icon.boss { filter: hue-rotate(0deg); }
.activity-icon.event { filter: hue-rotate(60deg); }

.activity-content {
  flex: 1;
}

.activity-text {
  font-weight: 500;
  color: var(--ea-text);
  margin-bottom: 0.25rem;
}

.activity-time {
  font-size: 0.8rem;
  color: var(--ea-muted);
}

/* Server Status Grid */
.server-status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.75rem;
  position: relative;
}

.status-indicator.online {
  background: #2ecc71;
  box-shadow: 0 0 10px rgba(46,204,113,0.5);
}

.status-indicator.warning {
  background: #f39c12;
  box-shadow: 0 0 10px rgba(243,156,18,0.5);
}

.status-indicator.offline {
  background: #e74c3c;
  box-shadow: 0 0 10px rgba(231,76,60,0.5);
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,0.8);
  animation: pulse 2s infinite;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 0.85rem;
  color: var(--ea-muted);
  margin-bottom: 0.25rem;
}

.status-value {
  font-weight: 600;
  color: var(--ea-text);
}

/* Quick Actions */
.quick-actions {
  margin-bottom: 2rem;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background: var(--ea-card-bg);
  border: 1px solid var(--ea-border);
  border-radius: 16px;
  text-decoration: none;
  color: var(--ea-text);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(204,184,109,0.1), transparent);
  transition: left 0.5s ease;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(204,184,109,0.2);
  border-color: var(--ea-gold);
  color: var(--ea-text);
  text-decoration: none;
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
}

.action-label {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Footer */
.footer-note {
  color: var(--ea-muted);
  font-size: 0.9rem;
  padding: 1rem;
  background: rgba(255,255,255,0.02);
  border: 1px solid rgba(255,255,255,0.05);
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stat-card {
    margin-bottom: 1rem;
  }

  .server-status-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    padding: 1rem;
  }

  .action-icon {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .hero {
    padding: 1.5rem;
  }

  .activity-card, .ranking-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 1.8rem;
  }

  .hero h3 {
    font-size: 1.5rem;
  }

  .ranking-title, .activity-title {
    font-size: 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .stat-card, .activity-card, .ranking-card, .action-card {
    border-color: rgba(204,184,109,.2);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .page-bg {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .stat-card, .activity-card, .ranking-card, .action-card {
    border-width: 2px;
    border-color: var(--ea-gold);
  }

  .player-name.elyos {
    color: #00bfff;
  }

  .player-name.asmodian {
    color: #ff4500;
  }
}

