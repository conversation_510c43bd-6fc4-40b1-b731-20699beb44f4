<?php
require_once __DIR__.'/app/auth.php';
start_session();
if(!isLoggedIn()) { header('Location: login.php'); exit; }

// Load basic LS data for display (schema-agnostic)
$db = db_ls();
$uid = $_SESSION['uid'];
$accountName = $_SESSION['username'] ?? '';

$hasTable = function($t) use ($db){
    try { return (bool)$db->queryFetchSingle("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$t]); }
    catch(Exception $e){ return false; }
};
$colExists = function($t,$c) use ($db){
    try {
        $r = $db->queryFetch("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$t]);
        if(!$r) return false; $set=[]; foreach($r as $x){ $set[strtoupper($x['COLUMN_NAME'])]=1; } return isset($set[strtoupper($c)]);
    } catch(Exception $e){ return false; }
};
$pickLoginTable = function() use ($hasTable,$colExists){
    foreach(['user_auth','user_account','account_data','accounts'] as $t){
        if($hasTable($t) && ($colExists($t,'account') || $colExists($t,'name'))){ return $t; }
    }
    return null;
};

$me = ['account' => $accountName, 'email' => $_SESSION['email'] ?? ''];
try{
    $loginTable = $pickLoginTable();
    if($loginTable){
        $acctCol = $colExists($loginTable,'account') ? 'account' : 'name';
        $uidCol = null; foreach(['uid','id','ssn','user_id','account_id'] as $c){ if($colExists($loginTable,$c)){ $uidCol=$c; break; } }
        $row = $db->queryFetchSingle("SELECT TOP 1 ua.$acctCol AS account".($uidCol?", ua.$uidCol AS raw_uid":"")." FROM dbo.$loginTable ua WHERE LOWER(ua.$acctCol)=LOWER(?)", [$accountName]);
        if(is_array($row)){
            $me['account'] = $row['account'] ?? $me['account'];
            $rawUid = $row['raw_uid'] ?? null;
            // Resolve email from web_account if present
            if($hasTable('web_account')){
                if($colExists('web_account','email') && $colExists('web_account','account')){
                    $wa = $db->queryFetchSingle("SELECT email, uid FROM dbo.web_account WHERE LOWER(account)=LOWER(?)", [$me['account']]);
                    if(is_array($wa)){ $me['email'] = $wa['email'] ?? $me['email']; $rawUid = $rawUid ?? ($wa['uid'] ?? null); }
                } elseif ($rawUid && $colExists('web_account','email') && $colExists('web_account','uid')){
                    $wa = $db->queryFetchSingle("SELECT email FROM dbo.web_account WHERE uid=?", [$rawUid]);
                    if(is_array($wa)) $me['email'] = $wa['email'] ?? $me['email'];
                }
            }
            // Optionally fallback to ssn.name
            if(empty($me['email']) && $hasTable('ssn') && $colExists('ssn','name') && $colExists('ssn','email')){
                $ssn = $db->queryFetchSingle("SELECT email FROM dbo.ssn WHERE LOWER(name)=LOWER(?)", [$me['account']]);
                if(is_array($ssn)) $me['email'] = $ssn['email'] ?? $me['email'];
            }
        }
    }
}catch(Exception $e){ /* leave defaults */ }
// Load credits from web_account (aioncms)
$credits = 0;
try{
  $dbw = db_web();
  if(!$dbw->offline){
    $rowc = $dbw->queryFetchSingle("SELECT donate_coin FROM web_account WHERE LOWER(account)=LOWER(?)", [$accountName]);
    if(is_array($rowc)) $credits = (int)($rowc['donate_coin'] ?? 0);
  }
}catch(Exception $e){ /* ignore */ }

?>
<!doctype html><html><head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dashboard - Elden Aion</title>
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/styles.css">
<link rel="stylesheet" href="css/symbol.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/cinzel.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/Philosopher.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/Montserrat.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/dashboard.css">
</head><body>
<nav class="navbar navbar-dark bg-dark"><div class="container">
<div class="page-bg"></div>
  <span class="navbar-brand">Elden Aion</span>
  <div class="d-flex">
    <a class="btn btn-outline-light me-2" href="usercp.php">Dashboard</a>
    <a class="btn btn-warning" href="logout.php">Logout</a>
  </div>
</div></nav>

<div class="container py-4">
  <div class="hero mb-4">
    <h3 class="mb-1">Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h3>
    <div class="kpi row g-3 mt-2">
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Account</small><div class="fw-bold"><?php echo htmlspecialchars($me['account'] ?? $_SESSION['username']); ?></div></div></div>
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Email</small><div class="fw-bold"><?php echo htmlspecialchars($me['email'] ?? $_SESSION['email']); ?></div></div></div>
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Credits</small><div class="fw-bold"><span class="badge-credits"><?php echo number_format($credits); ?></span></div></div></div>
    </div>
  </div>

  <div class="d-flex align-items-center justify-content-between mb-2">
    <h5 class="text-light">Featured Shop Items</h5>
    <a class="btn btn-sm btn-aion" href="/modules/shop.php">Open Full Shop</a>
  </div>
  <div id="shop-grid" class="shop-grid"></div>
  <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
    <h5 class="text-light">My Characters</h5>
  </div>
  <div class="char-grid" id="char-grid"></div>


  <div class="footer-note text-center mt-4">Tip: Use the Shop to purchase items delivered in-game via mail.</div>
</div>

<script>
(async function(){
  try{
    // Featured shop
    const res = await fetch('shop-preview.php');
    const data = res.ok ? await res.json() : {items:[]};
    const grid = document.getElementById('shop-grid');
    grid.innerHTML = '';
    (data.items||[]).slice(0,8).forEach(it => {
      const el = document.createElement('div');
      el.className = 'shop-card';
      const img = it.img || 'static/images/shop/default.gif';
      el.innerHTML = `
        <div class="thumb"><img src="${img}" alt="${it.name}"></div>
        <div class="body">
          <div class="d-flex justify-content-between align-items-center mb-1">
            <div class="fw-bold">${it.name}</div>
            <span class="badge-credits">${it.cost} tolls</span>
          </div>
          <a href="/modules/shop/item.php?id=${it.item_id}" class="btn btn-sm btn-aion w-100 mt-2">View</a>
        </div>`;
      grid.appendChild(el);
    });

    // Characters
    const charGrid = document.getElementById('char-grid');
    charGrid.innerHTML = '';
    const resp2 = await fetch('user-characters.php');
    if (resp2.ok) {
      const chars = await resp2.json();
      (chars||[]).forEach(c => {
        const cc = document.createElement('div');
        cc.className = 'char-card';
        cc.innerHTML = `<div class="name">${c.user_id}</div><div class="meta">Level ${c.level ?? '?'} • ${c.race ?? ''} ${c.player_class ?? ''}</div>`;
        charGrid.appendChild(cc);
      });
    }
  }catch(e){ console.warn(e); }
})();
</script>
</body></html>

