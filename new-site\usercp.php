<?php
require_once __DIR__.'/app/auth.php';
start_session();
if(!isLoggedIn()) { header('Location: login.php'); exit; }

// Load basic LS data for display (schema-agnostic)
$db = db_ls();
$uid = $_SESSION['uid'];
$accountName = $_SESSION['username'] ?? '';

$hasTable = function($t) use ($db){
    try { return (bool)$db->queryFetchSingle("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$t]); }
    catch(Exception $e){ return false; }
};
$colExists = function($t,$c) use ($db){
    try {
        $r = $db->queryFetch("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA='dbo' AND TABLE_NAME=?", [$t]);
        if(!$r) return false; $set=[]; foreach($r as $x){ $set[strtoupper($x['COLUMN_NAME'])]=1; } return isset($set[strtoupper($c)]);
    } catch(Exception $e){ return false; }
};
$pickLoginTable = function() use ($hasTable,$colExists){
    foreach(['user_auth','user_account','account_data','accounts'] as $t){
        if($hasTable($t) && ($colExists($t,'account') || $colExists($t,'name'))){ return $t; }
    }
    return null;
};

$me = ['account' => $accountName, 'email' => $_SESSION['email'] ?? ''];
try{
    $loginTable = $pickLoginTable();
    if($loginTable){
        $acctCol = $colExists($loginTable,'account') ? 'account' : 'name';
        $uidCol = null; foreach(['uid','id','ssn','user_id','account_id'] as $c){ if($colExists($loginTable,$c)){ $uidCol=$c; break; } }
        $row = $db->queryFetchSingle("SELECT TOP 1 ua.$acctCol AS account".($uidCol?", ua.$uidCol AS raw_uid":"")." FROM dbo.$loginTable ua WHERE LOWER(ua.$acctCol)=LOWER(?)", [$accountName]);
        if(is_array($row)){
            $me['account'] = $row['account'] ?? $me['account'];
            $rawUid = $row['raw_uid'] ?? null;
            // Resolve email from web_account if present
            if($hasTable('web_account')){
                if($colExists('web_account','email') && $colExists('web_account','account')){
                    $wa = $db->queryFetchSingle("SELECT email, uid FROM dbo.web_account WHERE LOWER(account)=LOWER(?)", [$me['account']]);
                    if(is_array($wa)){ $me['email'] = $wa['email'] ?? $me['email']; $rawUid = $rawUid ?? ($wa['uid'] ?? null); }
                } elseif ($rawUid && $colExists('web_account','email') && $colExists('web_account','uid')){
                    $wa = $db->queryFetchSingle("SELECT email FROM dbo.web_account WHERE uid=?", [$rawUid]);
                    if(is_array($wa)) $me['email'] = $wa['email'] ?? $me['email'];
                }
            }
            // Optionally fallback to ssn.name
            if(empty($me['email']) && $hasTable('ssn') && $colExists('ssn','name') && $colExists('ssn','email')){
                $ssn = $db->queryFetchSingle("SELECT email FROM dbo.ssn WHERE LOWER(name)=LOWER(?)", [$me['account']]);
                if(is_array($ssn)) $me['email'] = $ssn['email'] ?? $me['email'];
            }
        }
    }
}catch(Exception $e){ /* leave defaults */ }
// Load credits from web_account (aioncms)
$credits = 0;
try{
  $dbw = db_web();
  if(!$dbw->offline){
    $rowc = $dbw->queryFetchSingle("SELECT donate_coin FROM web_account WHERE LOWER(account)=LOWER(?)", [$accountName]);
    if(is_array($rowc)) $credits = (int)($rowc['donate_coin'] ?? 0);
  }
}catch(Exception $e){ /* ignore */ }

?>
<!doctype html><html><head>
<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dashboard - Elden Aion</title>
<link rel="stylesheet" href="css/bootstrap.min.css">
<link rel="stylesheet" href="css/styles.css">
<link rel="stylesheet" href="css/symbol.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/cinzel.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/Philosopher.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/Montserrat.css" media="print" onload="this.media='all'">
<link rel="stylesheet" href="css/dashboard.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head><body>
<nav class="navbar navbar-dark bg-dark"><div class="container">
<div class="page-bg">
  <!-- Floating Magical Particles -->
  <div class="magic-particles">
    <div class="particle particle-1"></div>
    <div class="particle particle-2"></div>
    <div class="particle particle-3"></div>
    <div class="particle particle-4"></div>
    <div class="particle particle-5"></div>
    <div class="particle particle-6"></div>
    <div class="particle particle-7"></div>
    <div class="particle particle-8"></div>
  </div>
</div>
  <span class="navbar-brand">Elden Aion</span>
  <div class="d-flex">
    <a class="btn btn-outline-light me-2" href="usercp.php">Dashboard</a>
    <a class="btn btn-warning" href="logout.php">Logout</a>
  </div>
</div></nav>

<div class="container py-4">
  <div class="hero mb-4">
    <h3 class="mb-1">Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h3>
    <div class="kpi row g-3 mt-2">
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Account</small><div class="fw-bold"><?php echo htmlspecialchars($me['account'] ?? $_SESSION['username']); ?></div></div></div>
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Email</small><div class="fw-bold"><?php echo htmlspecialchars($me['email'] ?? $_SESSION['email']); ?></div></div></div>
      <div class="col-md-4"><div class="card p-3"><small class="text-muted">Credits</small><div class="fw-bold"><span class="badge-credits"><?php echo number_format($credits); ?></span></div></div></div>
    </div>
  </div>

  <!-- Server Statistics Section -->
  <div class="server-stats mb-4" data-aos="fade-up">
    <h5 class="text-light mb-3 section-title">
      <i class="fas fa-server me-2 icon-glow"></i>Server Statistics
      <div class="title-underline"></div>
    </h5>
    <div class="row g-3">
      <div class="col-md-3">
        <div class="stat-card elyos-card">
          <div class="stat-icon">⚔️</div>
          <div class="stat-content">
            <div class="stat-number" id="elyos-count">-</div>
            <div class="stat-label">Elyos Online</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card asmo-card">
          <div class="stat-icon">🛡️</div>
          <div class="stat-content">
            <div class="stat-number" id="asmo-count">-</div>
            <div class="stat-label">Asmodians Online</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card total-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <div class="stat-number" id="total-online">-</div>
            <div class="stat-label">Total Online</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-card level-card">
          <div class="stat-icon">⭐</div>
          <div class="stat-content">
            <div class="stat-number" id="avg-level">-</div>
            <div class="stat-label">Average Level</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Top Players Section -->
  <div class="top-players mb-4" data-aos="fade-up" data-aos-delay="200">
    <h5 class="text-light mb-3 section-title">
      <i class="fas fa-trophy me-2 icon-glow"></i>Top Players
      <div class="title-underline"></div>
    </h5>
    <div class="row">
      <div class="col-md-6">
        <div class="ranking-card">
          <h6 class="ranking-title">🏆 Highest Level</h6>
          <div id="top-level-players" class="player-list">
            <div class="loading-placeholder">Loading...</div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="ranking-card">
          <h6 class="ranking-title">⚔️ PvP Champions</h6>
          <div id="top-pvp-players" class="player-list">
            <div class="loading-placeholder">Loading...</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity & Events Section -->
  <div class="activity-section mb-4">
    <div class="row">
      <div class="col-md-6">
        <div class="activity-card">
          <h6 class="activity-title">
            <i class="fas fa-clock me-2"></i>Recent Server Events
          </h6>
          <div id="recent-events" class="activity-list">
            <div class="activity-item">
              <div class="activity-icon siege">🏰</div>
              <div class="activity-content">
                <div class="activity-text">Fortress Siege in Reshanta</div>
                <div class="activity-time">2 hours ago</div>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-icon boss">👹</div>
              <div class="activity-content">
                <div class="activity-text">World Boss Defeated</div>
                <div class="activity-time">4 hours ago</div>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-icon event">🎉</div>
              <div class="activity-content">
                <div class="activity-text">Double XP Event Started</div>
                <div class="activity-time">6 hours ago</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="activity-card">
          <h6 class="activity-title">
            <i class="fas fa-chart-line me-2"></i>Server Status
          </h6>
          <div class="server-status-grid">
            <div class="status-item">
              <div class="status-indicator online"></div>
              <div class="status-info">
                <div class="status-label">Login Server</div>
                <div class="status-value">Online</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-indicator online"></div>
              <div class="status-info">
                <div class="status-label">Game Server</div>
                <div class="status-value">Online</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-indicator warning"></div>
              <div class="status-info">
                <div class="status-label">Server Load</div>
                <div class="status-value">Medium</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-indicator online"></div>
              <div class="status-info">
                <div class="status-label">Uptime</div>
                <div class="status-value">99.8%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex align-items-center justify-content-between mb-2">
    <h5 class="text-light">
      <i class="fas fa-shopping-cart me-2"></i>Featured Shop Items
    </h5>
    <a class="btn btn-sm btn-aion" href="/modules/shop.php">
      <i class="fas fa-external-link-alt me-1"></i>Open Full Shop
    </a>
  </div>
  <div id="shop-grid" class="shop-grid"></div>
  <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
    <h5 class="text-light">
      <i class="fas fa-users me-2"></i>My Characters
    </h5>
    <a class="btn btn-sm btn-outline-light" href="user-characters.php">
      <i class="fas fa-eye me-1"></i>View All
    </a>
  </div>
  <div class="char-grid" id="char-grid"></div>

  <!-- Quick Actions Section -->
  <div class="quick-actions mt-4 mb-4">
    <h5 class="text-light mb-3">
      <i class="fas fa-bolt me-2"></i>Quick Actions
    </h5>
    <div class="row g-3">
      <div class="col-md-3">
        <a href="/modules/shop.php" class="action-card">
          <div class="action-icon">🛒</div>
          <div class="action-label">Shop</div>
        </a>
      </div>
      <div class="col-md-3">
        <a href="/modules/ranking.php" class="action-card">
          <div class="action-icon">🏆</div>
          <div class="action-label">Rankings</div>
        </a>
      </div>
      <div class="col-md-3">
        <a href="/modules/guild.php" class="action-card">
          <div class="action-icon">⚔️</div>
          <div class="action-label">Guilds</div>
        </a>
      </div>
      <div class="col-md-3">
        <a href="/modules/events.php" class="action-card">
          <div class="action-icon">🎉</div>
          <div class="action-label">Events</div>
        </a>
      </div>
    </div>
  </div>

  <div class="footer-note text-center mt-4">
    <i class="fas fa-info-circle me-2"></i>
    Tip: Use the Shop to purchase items delivered in-game via mail. Server statistics update every 30 seconds.
  </div>
</div>

<script>
(async function(){
  try{
    // Load server statistics
    const statsRes = await fetch('server-stats.php');
    if (statsRes.ok) {
      const stats = await statsRes.json();

      // Update server statistics
      document.getElementById('elyos-count').textContent = stats.elyos_online || 0;
      document.getElementById('asmo-count').textContent = stats.asmo_online || 0;
      document.getElementById('total-online').textContent = stats.total_online || 0;
      document.getElementById('avg-level').textContent = stats.avg_level || '0';

      // Update top level players
      const topLevelContainer = document.getElementById('top-level-players');
      topLevelContainer.innerHTML = '';
      (stats.top_level_players || []).forEach((player, index) => {
        const playerEl = document.createElement('div');
        playerEl.className = 'player-rank-item';
        playerEl.innerHTML = `
          <div class="rank-number">#${index + 1}</div>
          <div class="player-info">
            <div class="player-name ${player.race.toLowerCase()}">${player.name}</div>
            <div class="player-details">Level ${player.level} • ${player.class}</div>
          </div>
        `;
        topLevelContainer.appendChild(playerEl);
      });

      // Update top PvP players
      const topPvpContainer = document.getElementById('top-pvp-players');
      topPvpContainer.innerHTML = '';
      (stats.top_pvp_players || []).forEach((player, index) => {
        const playerEl = document.createElement('div');
        playerEl.className = 'player-rank-item';
        playerEl.innerHTML = `
          <div class="rank-number">#${index + 1}</div>
          <div class="player-info">
            <div class="player-name ${player.race.toLowerCase()}">${player.name}</div>
            <div class="player-details">${player.kills} kills • ${player.class}</div>
          </div>
        `;
        topPvpContainer.appendChild(playerEl);
      });
    }

    // Featured shop
    const res = await fetch('shop-preview.php');
    const data = res.ok ? await res.json() : {items:[]};
    const grid = document.getElementById('shop-grid');
    grid.innerHTML = '';
    (data.items||[]).slice(0,8).forEach(it => {
      const el = document.createElement('div');
      el.className = 'shop-card';
      const img = it.img || 'static/images/shop/default.gif';
      el.innerHTML = `
        <div class="thumb"><img src="${img}" alt="${it.name}"></div>
        <div class="body">
          <div class="d-flex justify-content-between align-items-center mb-1">
            <div class="fw-bold">${it.name}</div>
            <span class="badge-credits">${it.cost} tolls</span>
          </div>
          <a href="/modules/shop/item.php?id=${it.item_id}" class="btn btn-sm btn-aion w-100 mt-2">View</a>
        </div>`;
      grid.appendChild(el);
    });

    // Characters
    const charGrid = document.getElementById('char-grid');
    charGrid.innerHTML = '';
    const resp2 = await fetch('user-characters.php');
    if (resp2.ok) {
      const chars = await resp2.json();
      (chars||[]).forEach(c => {
        const cc = document.createElement('div');
        cc.className = 'char-card';
        cc.innerHTML = `<div class="name">${c.user_id}</div><div class="meta">Level ${c.level ?? '?'} • ${c.race ?? ''} ${c.player_class ?? ''}</div>`;
        charGrid.appendChild(cc);
      });
    }
  }catch(e){ console.warn(e); }
})();

// Auto-refresh server stats every 30 seconds
setInterval(async () => {
  try {
    const statsRes = await fetch('server-stats.php');
    if (statsRes.ok) {
      const stats = await statsRes.json();

      // Animate number changes
      const animateNumber = (elementId, newValue) => {
        const element = document.getElementById(elementId);
        const currentValue = parseInt(element.textContent) || 0;
        if (currentValue !== newValue) {
          element.style.transform = 'scale(1.1)';
          element.textContent = newValue;
          setTimeout(() => {
            element.style.transform = 'scale(1)';
          }, 200);
        }
      };

      animateNumber('elyos-count', stats.elyos_online || 0);
      animateNumber('asmo-count', stats.asmo_online || 0);
      animateNumber('total-online', stats.total_online || 0);

      const avgLevelEl = document.getElementById('avg-level');
      if (avgLevelEl.textContent !== (stats.avg_level || '0')) {
        avgLevelEl.style.transform = 'scale(1.1)';
        avgLevelEl.textContent = stats.avg_level || '0';
        setTimeout(() => {
          avgLevelEl.style.transform = 'scale(1)';
        }, 200);
      }
    }
  } catch(e) {
    console.warn('Failed to refresh stats:', e);
  }
}, 30000);

// Simple AOS (Animate On Scroll) implementation
function initAOS() {
  const elements = document.querySelectorAll('[data-aos]');

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const delay = entry.target.getAttribute('data-aos-delay') || 0;
        setTimeout(() => {
          entry.target.classList.add('aos-animate');
        }, delay);
      }
    });
  }, { threshold: 0.1 });

  elements.forEach(el => observer.observe(el));
}

// Add magical hover effects
function addMagicalEffects() {
  const cards = document.querySelectorAll('.stat-card, .ranking-card, .activity-card');

  cards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-8px) scale(1.02)';

      // Add sparkle effect
      const sparkle = document.createElement('div');
      sparkle.innerHTML = '✨';
      sparkle.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 1.2rem;
        pointer-events: none;
        animation: sparkle 0.6s ease-out;
        z-index: 10;
      `;
      this.appendChild(sparkle);

      setTimeout(() => sparkle.remove(), 600);
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = '';
    });
  });
}

// Add click ripple effect
function addRippleEffect() {
  const clickableElements = document.querySelectorAll('.stat-card, .action-card, .btn-aion');

  clickableElements.forEach(element => {
    element.addEventListener('click', function(e) {
      const ripple = document.createElement('div');
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(255,215,0,0.3), transparent 70%);
        border-radius: 50%;
        pointer-events: none;
        animation: ripple 0.6s ease-out;
        z-index: 1;
      `;

      this.style.position = 'relative';
      this.appendChild(ripple);

      setTimeout(() => ripple.remove(), 600);
    });
  });
}

// Initialize all effects
document.addEventListener('DOMContentLoaded', function() {
  initAOS();
  addMagicalEffects();
  addRippleEffect();
});

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    0% { transform: scale(0); opacity: 0.5; }
    100% { transform: scale(1); opacity: 0; }
  }

  @keyframes sparkle {
    0% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
    100% { opacity: 0; transform: scale(0) rotate(360deg); }
  }
`;
document.head.appendChild(style);
</script>
</body></html>

