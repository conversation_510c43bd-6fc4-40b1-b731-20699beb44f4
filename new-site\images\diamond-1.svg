<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.293 11L11 21.293L0.707031 11L11 0.707031L21.293 11Z" stroke="#FFF260" stroke-opacity="0.2"/>
<g filter="url(#filter0_i_124_312)">
<g clip-path="url(#paint0_angular_124_312_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0 -0.00572 0.00572 0 11 11)"><foreignObject x="-1174.83" y="-1174.83" width="2349.65" height="2349.65"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(192, 117, 12, 1) 0deg,rgba(255, 241, 95, 1) 250.962deg,rgba(255, 236, 160, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M11 5.28003L16.72 11L11 16.72L5.27997 11L11 5.28003Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.75480771064758301,&#34;g&#34;:0.45996093750,&#34;b&#34;:0.047175481915473938,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.94791668653488159,&#34;b&#34;:0.3750,&#34;a&#34;:1.0},&#34;position&#34;:0.69711536169052124},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92596155405044556,&#34;b&#34;:0.62980771064758301,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.75480771064758301,&#34;g&#34;:0.45996093750,&#34;b&#34;:0.047175481915473938,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.94791668653488159,&#34;b&#34;:0.3750,&#34;a&#34;:1.0},&#34;position&#34;:0.69711536169052124},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:0.92596155405044556,&#34;b&#34;:0.62980771064758301,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-2.1014944596103131e-15,&#34;m01&#34;:11.440002441406250,&#34;m02&#34;:5.279968261718750,&#34;m10&#34;:-11.440002441406250,&#34;m11&#34;:-2.1014944596103131e-15,&#34;m12&#34;:16.720031738281250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<path d="M16.0124 10.9998L10.9997 16.0125L5.98602 10.9998L10.9997 5.98608L16.0124 10.9998Z" stroke="#FFF260" stroke-opacity="0.2"/>
<defs>
<filter id="filter0_i_124_312" x="5.27997" y="5.28003" width="11.44" height="12.4399" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.709804 0 0 0 0 1 0 0 0 0 0.490196 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_124_312"/>
</filter>
<clipPath id="paint0_angular_124_312_clip_path"><path d="M11 5.28003L16.72 11L11 16.72L5.27997 11L11 5.28003Z"/></clipPath></defs>
</svg>
