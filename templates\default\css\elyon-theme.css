/* ===================================
   ELYON AION INSPIRED THEME
   Complete transformation to match the reference design
   =================================== */

/* CSS Variables for consistent theming */
:root {
    --primary-gold: #D4AF37;
    --dark-gold: #B8860B;
    --light-gold: #FFD700;
    --gold-text: #F4E4BC;
    --dark-bg: #0a0a0a;
    --darker-bg: #000000;
    --card-bg: rgba(26, 26, 26, 0.95);
    --border-gold: rgba(212, 175, 55, 0.3);
    --text-light: #e0eaf3;
    --text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Global Reset and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    min-height: 100vh;
    font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--darker-bg);
    color: var(--text-light);
    overflow-x: hidden;
}

/* Full-screen background with overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)),
        url('../img/elyon-bg.jpg') center/cover no-repeat;
    z-index: -2;
    pointer-events: none;
}

/* Animated particles overlay */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/particles.png') repeat;
    opacity: 0.1;
    z-index: -1;
    pointer-events: none;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(0px) translateX(-5px); }
    75% { transform: translateY(10px) translateX(5px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--light-gold);
    text-shadow: var(--text-shadow);
    font-weight: 600;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--light-gold), var(--primary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }

p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-light);
}

a {
    color: var(--primary-gold);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--light-gold);
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

/* Main wrapper styling */
.main-wrapper {
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

.page-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* Card styling with gaming aesthetic */
.card {
    background: linear-gradient(135deg, 
        rgba(26, 26, 26, 0.95) 0%, 
        rgba(44, 24, 16, 0.95) 100%);
    border: 2px solid var(--border-gold);
    border-radius: 15px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(212, 175, 55, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent, 
        var(--primary-gold), 
        transparent);
    opacity: 0.6;
}

.card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

/* Gaming-style buttons */
.btn, button, input[type="submit"] {
    background: linear-gradient(135deg, 
        var(--dark-gold) 0%, 
        var(--primary-gold) 100%);
    border: 2px solid var(--primary-gold);
    color: var(--darker-bg);
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-shadow: none;
}

.btn::before, button::before, input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
    transition: left 0.5s ease;
}

.btn:hover::before, button:hover::before, input[type="submit"]:hover::before {
    left: 100%;
}

.btn:hover, button:hover, input[type="submit"]:hover {
    background: linear-gradient(135deg, 
        var(--primary-gold) 0%, 
        var(--light-gold) 100%);
    box-shadow: 
        0 5px 15px rgba(212, 175, 55, 0.4),
        0 0 20px rgba(255, 215, 0, 0.3);
    transform: translateY(-2px);
}

/* Form styling */
.form-control, input[type="text"], input[type="email"], 
input[type="password"], textarea, select {
    background: rgba(17, 25, 39, 0.8);
    border: 2px solid var(--border-gold);
    border-radius: 8px;
    color: var(--text-light);
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-control:focus, input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
    background: rgba(17, 25, 39, 0.9);
}

.form-control::placeholder, input::placeholder, textarea::placeholder {
    color: rgba(224, 234, 243, 0.6);
}

/* Auth page specific styling */
.auth-page {
    max-width: 1200px;
    margin: 0 auto;
}

.auth-left-wrapper-pic {
    background: linear-gradient(135deg, 
        rgba(212, 175, 55, 0.1) 0%, 
        rgba(255, 215, 0, 0.05) 100%);
    border-radius: 15px 0 0 15px;
    position: relative;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.auth-left-wrapper-pic::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/character-silhouette.png') center/contain no-repeat;
    opacity: 0.3;
    filter: sepia(1) hue-rotate(35deg) saturate(2);
}

/* Navigation Bar Styling */
.elyon-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg,
        rgba(9, 9, 9, 0.95) 0%,
        rgba(26, 26, 26, 0.95) 100%);
    backdrop-filter: blur(15px);
    border-bottom: 2px solid var(--border-gold);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
}

.elyon-navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(180deg,
        rgba(0, 0, 0, 0.8) 0%,
        transparent 100%);
    z-index: -1;
    pointer-events: none;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--light-gold);
    text-shadow: var(--text-shadow);
}

.navbar-brand img {
    height: 40px;
    margin-right: 10px;
    filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-light);
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
    position: relative;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.nav-link:hover {
    color: var(--light-gold);
    background: rgba(212, 175, 55, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--primary-gold);
    background: rgba(212, 175, 55, 0.2);
    box-shadow: inset 0 0 10px rgba(212, 175, 55, 0.3);
}

/* Download/Action Button in Navbar */
.navbar-download {
    background: linear-gradient(135deg,
        rgb(106, 36, 8) 0%,
        rgb(183, 60, 11) 100%);
    color: rgb(255, 240, 211);
    padding: 12px 24px;
    border-radius: 8px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.navbar-download:hover {
    filter: brightness(130%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(183, 60, 11, 0.4);
}

.navbar-download::before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background: url('../img/particles.png') repeat;
    opacity: 0.3;
    animation: particleRotate 4s linear infinite;
    pointer-events: none;
}

@keyframes particleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Full-page hero section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-top: 70px;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg,
        var(--light-gold),
        var(--primary-gold),
        #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 0 0 30px rgba(255, 215, 0, 0.5); }
    100% { text-shadow: 0 0 50px rgba(255, 215, 0, 0.8); }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-btn {
    padding: 15px 40px;
    font-size: 1.1rem;
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 200px;
}

.hero-btn-primary {
    background: linear-gradient(135deg,
        var(--dark-gold) 0%,
        var(--primary-gold) 100%);
    border: 2px solid var(--primary-gold);
    color: var(--darker-bg);
}

.hero-btn-secondary {
    background: transparent;
    border: 2px solid var(--primary-gold);
    color: var(--primary-gold);
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
}

/* Character showcase area */
.character-showcase {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.character-left, .character-right {
    position: absolute;
    top: 0;
    height: 100%;
    width: 30%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.8;
}

.character-left {
    left: 0;
    background-image: url('../img/character-left.png');
    background-position: left center;
}

.character-right {
    right: 0;
    background-image: url('../img/character-right.png');
    background-position: right center;
}

/* Responsive design */
@media (max-width: 768px) {
    .page-wrapper {
        padding: 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }

    .auth-left-wrapper-pic {
        border-radius: 15px 15px 0 0;
        min-height: 200px;
    }

    .elyon-navbar {
        padding: 0 1rem;
    }

    .navbar-nav {
        gap: 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .character-left, .character-right {
        display: none;
    }
}

/* About Section Styling */
.about-section {
    padding: 5rem 0;
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.9) 0%,
        rgba(44, 24, 16, 0.9) 100%);
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../img/texture-overlay.png') repeat;
    opacity: 0.1;
    pointer-events: none;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    opacity: 0.9;
}

.text-gold {
    color: var(--light-gold);
}

.section-divider {
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg,
        transparent,
        var(--primary-gold),
        transparent);
    margin: 0 auto 2rem auto;
}

.about-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto 4rem auto;
}

.about-card {
    background: rgba(26, 26, 26, 0.8);
    border: 2px solid var(--border-gold);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.about-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        var(--primary-gold),
        transparent);
    opacity: 0.6;
}

.about-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(212, 175, 55, 0.2);
    border-color: var(--primary-gold);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: sepia(1) hue-rotate(35deg) saturate(2);
}

.about-card h3 {
    color: var(--light-gold);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.about-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    max-width: 1000px;
    margin: 0 auto;
}

.feature-item {
    background: rgba(17, 25, 39, 0.8);
    border: 1px solid var(--border-gold);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(17, 25, 39, 0.9);
    border-color: var(--primary-gold);
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 1.5rem;
    filter: sepia(1) hue-rotate(35deg) saturate(2);
}

.feature-text {
    color: var(--text-light);
    font-weight: 500;
}

/* Override existing styles for better integration */
.job-card {
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.95) 0%,
        rgba(44, 24, 16, 0.95) 100%) !important;
    border: 2px solid var(--border-gold) !important;
    border-radius: 15px !important;
    backdrop-filter: blur(10px);
}

.job-card h2, .job-card h3, .job-card h4 {
    color: var(--light-gold) !important;
    text-shadow: var(--text-shadow) !important;
}

.job-card p, .job-card li {
    color: var(--text-light) !important;
}

.flash-anim {
    animation: titleGlow 3s ease-in-out infinite alternate;
}

/* Additional Animations and Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation classes */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* Improved dropdown styling */
.dropdown-menu {
    background: rgba(26, 26, 26, 0.95);
    border: 2px solid var(--border-gold);
    border-radius: 10px;
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.dropdown-item {
    color: var(--text-light);
    padding: 10px 20px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dropdown-item:hover {
    background: rgba(212, 175, 55, 0.2);
    color: var(--light-gold);
}

/* Improved table styling */
.table-dark {
    background: rgba(26, 26, 26, 0.9);
    color: var(--text-light);
}

.table-dark th {
    background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
    color: var(--darker-bg);
    border-color: var(--border-gold);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.table-dark td {
    border-color: var(--border-gold);
    background: rgba(26, 26, 26, 0.8);
}

.table-dark tbody tr:hover {
    background: rgba(212, 175, 55, 0.1);
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-gold);
    border-radius: 50%;
    border-top-color: var(--primary-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tooltip styling */
.tooltip {
    background: rgba(26, 26, 26, 0.95);
    color: var(--text-light);
    border: 1px solid var(--border-gold);
    border-radius: 5px;
    backdrop-filter: blur(10px);
}

.tooltip-arrow {
    border-top-color: var(--border-gold);
}

/* Alert styling */
.alert {
    border-radius: 10px;
    border: 2px solid;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    border-color: rgba(40, 167, 69, 0.5);
    color: #d4edda;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.5);
    color: #f8d7da;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.5);
    color: #fff3cd;
}

.alert-info {
    background: rgba(23, 162, 184, 0.2);
    border-color: rgba(23, 162, 184, 0.5);
    color: #d1ecf1;
}

/* Progress bar styling */
.progress {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid var(--border-gold);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
    transition: width 0.6s ease;
}

/* Badge styling */
.badge {
    background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
    color: var(--darker-bg);
    border-radius: 15px;
    padding: 5px 12px;
    font-weight: 600;
    text-shadow: none;
}

/* Modal styling */
.modal-content {
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.95) 0%,
        rgba(44, 24, 16, 0.95) 100%);
    border: 2px solid var(--border-gold);
    border-radius: 15px;
    backdrop-filter: blur(15px);
}

.modal-header {
    border-bottom: 2px solid var(--border-gold);
}

.modal-footer {
    border-top: 2px solid var(--border-gold);
}

.modal-title {
    color: var(--light-gold);
    text-shadow: var(--text-shadow);
}
