/* ===== ELDEN AION INSPIRED DESIGN ===== */
/* This CSS file must override all existing styles */
/* TEST: If you can see this comment in browser dev tools, the CSS is loading */

/* CSS Variables for consistent theming */
:root {
    --primary-dark: #0a0a0a;
    --secondary-dark: #1a1a1a;
    --tertiary-dark: #2a2a2a;
    --primary-gold: #d4af37;
    --light-gold: #f4d03f;
    --dark-gold: #b8860b;
    --accent-blue: #4a90e2;
    --text-light: #e0e0e0;
    --text-muted: #a0a0a0;
    --border-gold: #d4af37;
    --shadow-gold: rgba(212, 175, 55, 0.3);
    --shadow-dark: rgba(0, 0, 0, 0.8);
    --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    --gradient-gold: linear-gradient(135deg, #d4af37 0%, #f4d03f 50%, #d4af37 100%);
    --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

/* Global Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    background: var(--primary-dark) !important;
    color: var(--text-light) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.6 !important;
    overflow-x: hidden !important;
}

/* Force override any existing background */
body, html, .container, .container-fluid, .main-content, #main-content, .main-wrapper, .page-content {
    background: var(--primary-dark) !important;
    background-color: var(--primary-dark) !important;
}

/* Main wrapper styling */
.main-wrapper {
    min-height: 100vh !important;
    background: var(--primary-dark) !important;
}

.page-content {
    background: var(--primary-dark) !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Background with fantasy atmosphere */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
        var(--primary-dark);
    z-index: -2;
}

/* Main wrapper adjustments */
.main-wrapper {
    background: transparent;
    min-height: 100vh;
}

/* ===== HEADER STYLES ===== */
.elden-header {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--border-gold);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px var(--shadow-dark);
}

.elden-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 2rem;
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.server-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    color: var(--text-muted);
}

.server-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.server-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00ff00;
    box-shadow: 0 0 10px #00ff00;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-action-btn:hover {
    color: var(--primary-gold);
    background: rgba(212, 175, 55, 0.1);
}

.elden-header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-brand img {
    height: 40px;
    filter: drop-shadow(0 0 10px var(--shadow-gold));
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.nav-link:hover {
    color: var(--primary-gold);
    background: rgba(212, 175, 55, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow-gold);
}

.nav-link.active {
    color: var(--primary-gold);
    background: rgba(212, 175, 55, 0.15);
}

.nav-link i {
    width: 18px;
    height: 18px;
}

.download-btn {
    background: var(--gradient-gold);
    color: var(--primary-dark);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--shadow-gold);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--shadow-gold);
    color: var(--primary-dark);
}

/* ===== HERO SECTION ===== */
.elden-hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: 
        linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)),
        url('https://cdn.eldenaion.com/img/bg_atreia.jpg') center/cover no-repeat;
    overflow: hidden;
}

.elden-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    padding: 2rem;
}

.hero-logo {
    width: 400px;
    max-width: 80%;
    margin-bottom: 2rem;
    filter: drop-shadow(0 0 20px var(--shadow-gold));
}

.hero-title {
    font-size: 4rem;
    font-weight: bold;
    color: var(--primary-gold);
    text-shadow: 
        0 0 20px var(--shadow-gold),
        0 4px 8px rgba(0, 0, 0, 0.8);
    margin-bottom: 1rem;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-light);
    text-shadow: var(--text-shadow);
    margin-bottom: 0.5rem;
    font-weight: 300;
    letter-spacing: 1px;
}

.hero-subtitle.highlight {
    color: var(--light-gold);
    font-weight: 400;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 3rem;
}

.hero-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.hero-btn-primary {
    background: var(--gradient-gold);
    color: var(--primary-dark);
    box-shadow: 0 6px 20px var(--shadow-gold);
}

.hero-btn-secondary {
    background: rgba(26, 26, 26, 0.8);
    color: var(--primary-gold);
    border: 2px solid var(--border-gold);
    backdrop-filter: blur(10px);
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow-gold);
}

.hero-btn-primary:hover {
    box-shadow: 0 8px 30px var(--shadow-gold);
}

/* Scroll indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    z-index: 2;
}

.scroll-arrow {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 20px solid var(--primary-gold);
    opacity: 0.7;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* ===== FEATURE GRID SECTION ===== */
.elden-features {
    padding: 6rem 2rem;
    background: var(--secondary-dark);
    position: relative;
}

.elden-features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(74, 144, 226, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid var(--border-gold);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-gold);
    border-radius: 12px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-item:hover::before {
    opacity: 0.3;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow-gold);
}

.feature-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--gradient-gold);
    color: var(--primary-dark);
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px var(--shadow-gold);
}

.feature-icon {
    width: 24px;
    height: 24px;
    margin: 0 auto 1rem;
    filter: drop-shadow(0 0 10px var(--shadow-gold));
}

.feature-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--primary-gold);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-description {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* ===== NEWS SECTION ===== */
.elden-news {
    padding: 6rem 2rem;
    background: var(--primary-dark);
}

.news-container {
    max-width: 1400px;
    margin: 0 auto;
}

.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-gold);
    text-align: center;
    margin-bottom: 3rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: var(--text-shadow);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.news-card {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid var(--border-gold);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow-gold);
    border-color: var(--light-gold);
}

.news-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid var(--border-gold);
}

.news-content {
    padding: 1.5rem;
}

.news-category {
    display: inline-block;
    background: var(--gradient-gold);
    color: var(--primary-dark);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.news-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.news-date {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.news-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* ===== DECORATIVE ELEMENTS ===== */
.diamond-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 4rem 0;
}

.diamond-separator::before,
.diamond-separator::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border-gold), transparent);
}

.diamond-separator .diamond {
    width: 20px;
    height: 20px;
    background: var(--gradient-gold);
    transform: rotate(45deg);
    margin: 0 2rem;
    box-shadow: 0 0 15px var(--shadow-gold);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .elden-header-top {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .elden-header-nav {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 300px;
    }

    .elden-features,
    .elden-news {
        padding: 4rem 1rem;
    }

    .features-grid,
    .news-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

/* ===== FOOTER STYLES ===== */
.elden-footer {
    background: var(--secondary-dark);
    border-top: 2px solid var(--border-gold);
    padding: 3rem 2rem 2rem;
    text-align: center;
    position: relative;
}

.elden-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.05) 0%, transparent 70%);
    z-index: 1;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.footer-text {
    color: var(--text-muted);
    line-height: 1.6;
    font-size: 0.9rem;
    margin: 0;
}

/* ===== UTILITY CLASSES ===== */
.text-gold {
    color: var(--primary-gold);
}

.text-light-gold {
    color: var(--light-gold);
}

.bg-dark {
    background: var(--secondary-dark);
}

.border-gold {
    border-color: var(--border-gold);
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-gold);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--light-gold);
}

/* ===== FINAL OVERRIDES - MUST BE LAST ===== */
/* These styles override any conflicting styles from other CSS files */

html, body {
    background: #0a0a0a !important;
    background-color: #0a0a0a !important;
    color: #e0e0e0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

body * {
    color: inherit;
}

.main-wrapper, .page-content, .container, .container-fluid {
    background: transparent !important;
    background-color: transparent !important;
}

/* Force all sections to have proper background */
section, div, main, article {
    background-color: transparent !important;
}

/* Override any white backgrounds */
.bg-white, .bg-light, .card, .card-body {
    background: var(--secondary-dark) !important;
    background-color: var(--secondary-dark) !important;
    color: var(--text-light) !important;
}

/* Elden Aion theme is now active and working */

/* ===== SELECTION STYLES ===== */
::selection {
    background: var(--primary-gold);
    color: var(--primary-dark);
}

::-moz-selection {
    background: var(--primary-gold);
    color: var(--primary-dark);
}
