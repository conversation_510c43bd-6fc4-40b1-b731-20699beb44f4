<!-- Elden Aion Hero Section -->
<section class="elden-hero">
    <div class="hero-content">
        <img src="<?php template_img() ?>elden_logo.png" alt="Elden Aion" class="hero-logo">

        <h1 class="hero-title">ELDEN AION</h1>
        <p class="hero-subtitle">EUROPE AION PRIVATE SERVER</p>
        <p class="hero-subtitle highlight">CLASSIC 3.9 - RETAIL MECHANICS - NO P2W</p>

        <div class="hero-buttons">
            <a href="https://discord.gg/U7x3pAUuHR" target="_blank" class="hero-btn hero-btn-secondary">
                <i data-feather="message-circle"></i>
                <span>Join our Discord</span>
            </a>
            <a href="<?php echo __BASE_URL__; ?>register" class="hero-btn hero-btn-primary">
                <i data-feather="user-plus"></i>
                <span>Play free now</span>
            </a>
        </div>
    </div>

    <!-- Scroll indicator -->
    <div class="scroll-indicator">
        <div class="scroll-arrow"></div>
    </div>
</section>

<!-- Feature Grid Section -->
<section class="elden-features">
    <div class="features-container">
        <div class="diamond-separator">
            <div class="diamond"></div>
        </div>

        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-number">I</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                </div>
                <h3 class="feature-title">Official Build</h3>
                <p class="feature-description">All perfectly working content. No bugs, glitches or gameplay inconveniences, 100% functional and stable.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">II</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="8.5" cy="7" r="4"/>
                        <path d="m22 2-5 10-5-5 10-5z"/>
                    </svg>
                </div>
                <h3 class="feature-title">Classic Classes</h3>
                <p class="feature-description">Classic Classes and Level 60 cap, with balanced skills and equipment.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">III</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"/>
                        <circle cx="12" cy="13" r="3"/>
                    </svg>
                </div>
                <h3 class="feature-title">Latest Skins</h3>
                <p class="feature-description">Always enjoy the newest cosmetic content from both Retail and Classic.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">IV</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                    </svg>
                </div>
                <h3 class="feature-title">X3 Exp Rate</h3>
                <p class="feature-description">Balanced experience rate with leveling slowing possibility. Progress at your pace either fast or slow.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">V</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                </div>
                <h3 class="feature-title">Zero P2W/ No VIP</h3>
                <p class="feature-description">Completely Free to play and without donation, VIP or P2W. Play and progress for free.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">VI</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M5 12.55a11 11 0 0 1 14.08 0"/>
                        <path d="M1.42 9a16 16 0 0 1 21.16 0"/>
                        <path d="M8.53 16.11a6 6 0 0 1 6.95 0"/>
                        <line x1="12" y1="20" x2="12.01" y2="20"/>
                    </svg>
                </div>
                <h3 class="feature-title">Proxy Support</h3>
                <p class="feature-description">With Proxy support for North America and Asia providing minimal ping to all players worldwide.</p>
            </div>

            <div class="feature-item">
                <div class="feature-number">VII</div>
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                        <line x1="8" y1="21" x2="16" y2="21"/>
                        <line x1="12" y1="17" x2="12" y2="21"/>
                    </svg>
                </div>
                <h3 class="feature-title">Hosted in Europe</h3>
                <p class="feature-description">Server hosted in Germany in high quality and stable dedicated servers, ensuring lowest pings and best peering across the globe.</p>
            </div>
        </div>
    </div>
</section>

<!-- News Section -->
<section class="elden-news">
    <div class="news-container">
        <div class="diamond-separator">
            <div class="diamond"></div>
        </div>

        <h2 class="section-title">Latest News & Updates</h2>

        <div class="news-grid">
            <article class="news-card">
                <img src="<?php template_img() ?>news/update-notes.jpg" alt="Update Notes" class="news-image">
                <div class="news-content">
                    <span class="news-category">PATCH NOTES</span>
                    <h3 class="news-title">Update Notes - <?php echo date('d/m/Y'); ?></h3>
                    <p class="news-date"><?php echo date('d/m/Y'); ?></p>
                    <p class="news-excerpt">Greetings Elden Aion community, the latest update notes are now available. Click here for more information.</p>
                </div>
            </article>

            <article class="news-card">
                <img src="<?php template_img() ?>news/gift-code.jpg" alt="Gift Code" class="news-image">
                <div class="news-content">
                    <span class="news-category">GIFT CODE</span>
                    <h3 class="news-title">Gift Code - #FIRE</h3>
                    <p class="news-date"><?php echo date('d/m/Y'); ?></p>
                    <p class="news-excerpt">The latest gift code #FIRE has arrived! Redeem it today for a blazing reward!</p>
                </div>
            </article>

            <article class="news-card">
                <img src="<?php template_img() ?>news/summer-event.jpg" alt="Summer Event" class="news-image">
                <div class="news-content">
                    <span class="news-category">EVENT</span>
                    <h3 class="news-title">The Summer Pals</h3>
                    <p class="news-date"><?php echo date('d/m/Y'); ?></p>
                    <p class="news-excerpt">As the summer heat rises, it's time for unbearably cute animal outfits! Click here for more information.</p>
                </div>
            </article>

            <article class="news-card">
                <img src="<?php template_img() ?>news/lugbug-missions.jpg" alt="Lugbug Missions" class="news-image">
                <div class="news-content">
                    <span class="news-category">UPDATE</span>
                    <h3 class="news-title">Lugbug Missions - Updated</h3>
                    <p class="news-date"><?php echo date('d/m/Y'); ?></p>
                    <p class="news-excerpt">Lugbug's Missions have been updated. Take up the challenges of the new Daily Quests! Click here to learn more.</p>
                </div>
            </article>
        </div>
    </div>
</section>

<!-- Additional Features Section -->
<section class="elden-features" style="background: var(--primary-dark);">
    <div class="features-container">
        <div class="diamond-separator">
            <div class="diamond"></div>
        </div>

        <h2 class="section-title">Retail-Like Quality • Classic 3.9 • Fully Free</h2>

        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                        <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                        <line x1="12" y1="22.08" x2="12" y2="12"/>
                    </svg>
                </div>
                <h3 class="feature-title">Return to Tiamaranta</h3>
                <p class="feature-description">Patch 3.9 the most nostalgic and balanced patch, featuring maps like Tiamaranta and its Siege system, NPCs, and additional Instances and content.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                    </svg>
                </div>
                <h3 class="feature-title">High Quality</h3>
                <p class="feature-description">Retail Core. Geodata, skills, mechanics, and AI work like they should.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                        <circle cx="8.5" cy="7" r="4"/>
                        <path d="m22 2-5 10-5-5 10-5z"/>
                    </svg>
                </div>
                <h3 class="feature-title">No Broken Classes</h3>
                <p class="feature-description">No Gunner, Bard, or Aethertech. Just pure Classic 3.9.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                </div>
                <h3 class="feature-title">Multi-Language Support</h3>
                <p class="feature-description">Play in your native language, no setup needed. Full support for Deutsch, Français, English, Español, Русский, Türk, and Polski.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                        <circle cx="12" cy="16" r="1"/>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                    </svg>
                </div>
                <h3 class="feature-title">High Security</h3>
                <p class="feature-description">Our advanced security includes dual-layer anti-cheat on both server and client, blocking third-party software like AutoHotkey for a fair gaming experience.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M5 12.55a11 11 0 0 1 14.08 0"/>
                        <path d="M1.42 9a16 16 0 0 1 21.16 0"/>
                        <path d="M8.53 16.11a6 6 0 0 1 6.95 0"/>
                        <line x1="12" y1="20" x2="12.01" y2="20"/>
                    </svg>
                </div>
                <h3 class="feature-title">Ping & Stability</h3>
                <p class="feature-description">Tier 1 routes and premium proxies. Doesn't matter where you play from, it runs smooth.</p>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                </div>
                <h3 class="feature-title">No Pay to win</h3>
                <p class="feature-description">No Donation server, no money advantage. Everyone plays on equal ground with no P2W.</p>
            </div>
        </div>
    </div>
</section>

<!-- Quick Facts Section -->
<section class="elden-news" style="background: var(--secondary-dark);">
    <div class="news-container">
        <div class="diamond-separator">
            <div class="diamond"></div>
        </div>

        <h2 class="section-title">Quick Facts</h2>

        <div class="features-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
            <div class="feature-item">
                <h3 class="feature-title">What is Elden Aion server version?</h3>
                <p class="feature-description">Elden Aion is a 3.9 PTS C++ server based on the Retail version, guaranteeing a bug-free experience and providing a premium, high-quality gaming experience.</p>
            </div>

            <div class="feature-item">
                <h3 class="feature-title">Where is the server located?</h3>
                <p class="feature-description">The server is hosted in Germany, Europe, ensuring optimal latency and performance for all players across Europe.</p>
            </div>

            <div class="feature-item">
                <h3 class="feature-title">Which languages are supported?</h3>
                <p class="feature-description">The game supports multiple languages, including English, Russian, French, Spanish, Turkish, Polish, and German.</p>
            </div>

            <div class="feature-item">
                <h3 class="feature-title">What's special about Elden Aion?</h3>
                <p class="feature-description">We offer a 3.9 Old School retail experience with a maximum level cap of 60. No broken classes (Bard, Gunner, AT) are included, and all content up to level 60 is unlocked.</p>
            </div>

            <div class="feature-item">
                <h3 class="feature-title">What are the server rates?</h3>
                <p class="feature-description">Elden Aion runs with XP set to x3. This ensures balanced leveling and smooth endgame progression.</p>
            </div>

            <div class="feature-item">
                <h3 class="feature-title">Is there VIP packs or P2W Cash Shop?</h3>
                <p class="feature-description">No, unlike other servers, Elden Aion does not feature a VIP Packs or P2W Cash Shop. Our goal is to offer a fair experience for all players.</p>
            </div>
        </div>
    </div>
</section>



<div class="row medium-margin">
  <div class="col-md-12">
    <!-- NeoAion Presentation - Revamped -->
    <div class="job-card p-5 text-center flash-card" style="background-color: #f0f8ff; border: 3px solid #007bff; border-radius: 16px;">
      <h2 class="flash-anim" style="color: #007bff;">🔥 Welcome to <strong>NeoAion</strong> – The Ultimate PvP Arena with Full PvE Freedom 🔥</h2>
      <h4 style="color: #0056b3;">⚔️ Based on Version 4.6 — Official Retail Files — 100% Bug-Free ⚔️</h4>
      <br>
      <p style="font-size: 18px; color: #333; max-width: 900px; margin: auto;">
        <strong>NeoAion</strong> is not just another private server — it’s a fully optimized battlefield built for action-hungry warriors! Our philosophy is simple: <span style="color: #ff4500;"><strong>pure PvP fun with quality PvE always available</strong></span>. Whether you're here to clash steel in Tiamaranta or dive deep into classic dungeons, we’ve got you covered.
      </p>
      <br>
      <ul style="list-style: none; padding: 0; font-size: 17px; color: #333; text-align: left; max-width: 850px; margin: auto;">
        <li>🛡️ <strong>Flawless Retail Files:</strong> Enjoy smooth, stable gameplay with official quality and no bugs.</li>
        <li>⚔️ <strong>Immediate PvP Action:</strong> Jump into fast-paced PvP with custom zones, recharge towers, and full battle freedom.</li>
        <li>🌍 <strong>Custom PvP World:</strong> Fight over Tiamaranta’s Eye, redesigned for intense PvP with lootable NeoAion Coins and Medals.</li>
        <li>🆕 <strong>Instant Level 65:</strong> After your Ascension quest, one simple quest will instantly boost you to level 65 — no more grinding!</li>
        <li>🎁 <strong>Start Fully Equipped:</strong> Begin your journey with Daevanion Lv60 gear + stigma unlockers — no grind needed.</li>
        <li>💥 <strong>Double Bloodmark Boost:</strong> All Katalam quests now give x2 Bloodmarks to boost your PvP gearing.</li>
        <li>📈 <strong>No More Leveling:</strong> The leveling phase is gone — you’re PvP-ready from the very start.</li>
        <li>🏰 <strong>Full PvE Content:</strong> Over 50 instances available — from Dark Poeta to Eternal Bastion — all functional!</li>
        <li>🔄 <strong>Custom Drop Rates:</strong> Loot x2, Kinah x2, with easy access to vital resources and PvP materials.</li>
        <li>🛍️ <strong>Free Starter Shop:</strong> Grab gear, stigmas, and useful items to fast-track your progress without cost.</li>
        <li>👥 <strong>Built with the Community:</strong> We listen. All updates are voted and approved by our players.</li>
      </ul>
      <br>
      <h3 style="color: #007bff;">🎮 Whether you live for PvP, enjoy team dungeons, or just want a place to chill and grow — <strong>NeoAion</strong> welcomes you. Join the war, write your legend! ⚡</h3>
    </div>
  </div>
</div>
<div class="row medium-margin">
  <div class="col-md-12">
    <!-- Elyos Recruitment Promo -->
    <div class="job-card p-5 text-center flash-card" style="background-color: #e8f8f5; border: 3px solid #28a745; border-radius: 16px;">
      <h2 class="flash-anim" style="color: #218838;">🕊️ Help Balance the Factions — Join the Elyos Side Today! 🕊️</h2>
      <br>
      <p style="font-size: 18px; color: #2f4f4f; max-width: 900px; margin: auto;">
        We are actively looking for more <strong style="color: #007bff;">Elyos players</strong> to ensure balanced and fun PvP battles across NeoAion. To support this, we're offering a special gift to every new Elyos player!
      </p>
      <br>
      <ul style="list-style: none; padding: 0; font-size: 17px; color: #333; text-align: left; max-width: 850px; margin: auto;">
        <li>🎁 <strong>Exclusive Reward:</strong> A <strong>free permanent mount</strong> for all new Elyos characters.</li>
        <li>🆓 <strong>Easy to Get:</strong> Just create an Elyos character, complete the automatic quests to level 65, and you're ready.</li>
        <li>📨 <strong>Claim It:</strong> Once level 65, open a support ticket on our <a href="https://discord.gg/XvQB5YSpRM" target="_blank" style="color: #218838; font-weight: bold;">Discord</a> and we'll deliver your mount quickly.</li>
      </ul>
      <br>
      <p style="font-size: 17px; color: #2e8b57;">
        Support faction balance and ride in style — join the Elyos side and enjoy the benefits!
      </p>
    </div>
  </div>
</div>

<div class="row medium-margin">
  <div class="col-md-12">
    <!-- Level 65 and New Classes Info -->
    <div class="job-card p-5 text-center flash-card" style="background-color: #fff5f0; border: 3px solid #ff6600; border-radius: 16px;">
      <h2 class="flash-anim" style="color: #ff3300;">🚀 Major Update — Skip the Grind: Reach Level 65 Instantly! 🔓</h2>
      <br>
      <p style="font-size: 18px; color: #444; max-width: 900px; margin: auto;">
        We're excited to introduce a major quality-of-life update: <strong>you now reach level 65 automatically by completing just two quests</strong>. The starting zone quest takes you to level 9, and after Ascension, your first capital quest brings you straight to the top!
      </p>
      <br>
      <h4 style="color: #ff6600;">🆕 Create These New Classes Now:</h4>
      <ul style="list-style: none; padding: 0; font-size: 17px; color: #333; max-width: 800px; margin: 0 auto;">
        <li>🎶 <strong style="color: #cc33cc;">Bard</strong> – A melody of support and destruction with powerful healing and magic attacks.</li>
        <li>🔫 <strong style="color: #ff6600;">Gunner</strong> – Rain bullets and AoE damage while staying mobile and stylish.</li>
        <li>🤖 <strong style="color: #0099cc;">Aethertech</strong> – Dominate the battlefield with your combat mecha and crushing skills.</li>
      </ul>
      <br>
      <p style="font-size: 17px; color: #555; max-width: 850px; margin: auto;">
        These classes are available right from character creation and fully equipped to PvP immediately.
      </p>
      <br>
      <h4 style="color: #ff3300;">💥 True PvP-Ready Gameplay</h4>
      <p style="font-size: 17px; color: #444; max-width: 850px; margin: auto;">
        No need to farm levels — we want you in the action from the start. This update eliminates the grind and gets you into the fight instantly. Welcome to a real PvP experience.
      </p>
      <br>
      <p style="font-size: 17px; color: #333; font-style: italic;">
        NeoAion is now faster, more dynamic, and more fun than ever. Try it today and forge your legend!
      </p>
    </div>
  </div>
</div>



<div class="row medium-margin">
  <div class="col-md-12">
    <!-- Discord Change Alert -->
    <div class="job-card p-4 text-center flash-card" style="background-color: #e6f7ff; border: 2px solid #1890ff; border-radius: 10px;">
      <h5 class="colored flash-anim" style="color: #1890ff;">📢 Important Announcement – New Discord Available!</h5>
      <h6 class="colored" style="color: #1890ff;">🚨 We are moving to a brand new Discord server! 🚨</h6>
      <h6 class="colored" style="color: #1890ff;">This server will no longer be used for updates or announcements.</h6>
      <br>
      <p style="font-size: 16px; color: #333;">
        To stay updated on all the latest news, events, and support for NeoAion, please make sure to join our new Discord server!
      </p>
      <br>
      <a href="https://discord.gg/kQKmWwcywq" target="_blank" style="display: inline-block; background-color: #1890ff; color: white; padding: 12px 24px; border-radius: 6px; font-size: 18px; font-weight: bold; text-decoration: none;">
        👉 Join the New Discord Now!
      </a>
      <br><br>
      <p style="font-size: 14px; color: #888;">Thank you for your support – The NeoAion Team 💙</p>
    </div>
  </div>
</div>



<!--<div>
    <?php if (is_array($playerOnline)) { ?>
        <div class="row tiny-margin">
            <?php foreach ($playerOnline as $online) { ?>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"><?php echo $online['world_user'] + 3 ?></h2>
                        <p>Players Online</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Level 65 is here </h2>
                        <p>Go leveling your char</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Online rewards </h2>
                        <p>candies all 2 hours</p>
                    </div>    
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Rechargers </h2>
                        <p>To chain the duels</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Many Skins </h2>
                        <p>Skins / Mounts / pets from 5.8+</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Customs NPC </h2>
                        <p>to exchange NeoAion coins</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> customized events </h2>
                        <p>Create from NeoAion</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> PvP Farming Aera  </h2>
                        <p>for active PvP session</p>
                    </div>  
                </div>
                <div class="col-md-4">
                    <div class="job-card animated-info-card" style="background: linear-gradient(135deg, #fff5f5 80%, #ff4d4d22 100%); border: 2px solid #ff4d4d; border-radius: 18px; box-shadow: 0 4px 24px #ff4d4d22;">
                        <h2 class="colored animated-flash"> Rewards leveling </h2>
                        <p>for all level up !</p>
                    </div>  
                </div>
            <?php } ?>
        </div> -->
        <br><br><br>
    <?php } ?>
    <div class="row">
        <div class="card-deck animate-in" data-anim-type="fade-in-up">
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>Starterpack.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-gift"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Free starter pack
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            Right away in your inventory
                        </strong>
                    </h6>
                    <p>
                        Your starter pack is designed to kickstart your adventure to PvP ! 
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>instant65.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-arrow-up"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Level 65 in 3 min
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            Instant level 65 ! 
                        </strong>
                    </h6>
                    <p>
                         Do only 2 quests and get the level 65 ready for the PvP Battle !
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>perfect46.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-gamepad"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Perfect server 4.6
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            100% working capacity
                        </strong>
                    </h6>
                    <p>
                        Skills, geodata, NPCs, dungeons and everything else that may be in the game work perfectly.
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>euorpe.png?v=1" alt="...">
                <div class="news-card-ol">
                 <i class="fa fa-wifi"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Low ping for EU and Russia
                        </h4>
                    </div>

                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            Server located in Est france
                        </strong>
                    </h6>
                    <p>
                        The server is located Strasbourg in France, which allows to get a great ping from anywhere in Europe and Russia.
                    </p>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="row">
        <div class="card-deck animate-in" data-anim-type="fade-in-up">
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>Languages.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-globe"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Multilanguage support
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            Play in your native language
                        </strong>
                    </h6>
                    <p>
                         Our client supports: Deutsch, Français, English, Italiano, Español, Русский, Türk and chineese.
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>anticheat.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-shield"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Anticheat Active
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            For your security and confort
                        </strong>
                    </h6>
                    <p>
                        Built-in anti-cheat system from the server. Full protection from the third-party software and unscrupulous players.
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>shop.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-shopping-basket"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Game shop
                        </h4>
                    </div>
                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            100% PvP content
                        </strong>
                    </h6>
                    <p>
                       All in the shop can be farm in game + Some skins from 5.8 and 7.7 
                    </p>
                </div>
            </div>
            <div class="card news-card">
                <img class="card-img-top" src="<?php template_img() ?>event.png?v=1" alt="...">
                <div class="news-card-ol">
                    <i class="fa fa-clipboard"></i>
                </div>
                <div class="card-body news-card-body">
                    <div class="card-title">
                        <h4>
                            Weekly events ingame
                        </h4>
                    </div>

                    <h6>
                        <strong style="color: rgb(190, 47, 47);">
                            Many events on the server
                        </strong>
                    </h6>
                    <p>
                        Neo Aion on server have auto events and GM make tournament and Kor.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div> </br></br></br>

<div id="games" class="medium-margin">
    <a href="games"></a><!-- Nav Anchor -->
    <div class="container">
        <div class="row heading tiny-margin">
            <div class="col-md-auto">
                <h1 class="animation-element slide-down">
                    Download <span class="colored">NeoAion</span>
                </h1>
            </div>
            <div class="col">
                <hr class="animation-element extend">
            </div>
        </div>
        <div class="row medium-margin">
            <div class="col-md-11">
                <h2 class="short-hr-left">
                    How to Download and Install NeoAion
                </h2>
                <p>
                    There are two ways to install the game. Please read carefully and choose the method that suits you best.
                </p>
                <br>
            </div> 

            <!-- Solution 1: Launcher (Recommended) -->
            <div class="col-md-6">
                <div class="card text-center shadow-sm" style="border: 1px solid #28a745; border-radius: 10px;">
                    <div class="card-body">
                        <h3 class="colored">Solution 1: Install with Launcher <span style="font-size:0.8em;" class="badge badge-success">Recommended</span></h3>
                        <p>
                            <strong>Fastest and easiest way!</strong><br>
                            Download the NeoAion Launcher and run it. The launcher will automatically download and install the full game for you.<br>
                            <span class="text-muted" style="font-size:0.95em;">No manual extraction or torrent needed.</span>
                        </p>
                        <a href="https://www.neoaion.fr/dl/NeoAion_0.1.0_x64-setup.exe" target="_blank" class="button button-flash" style="margin-bottom:8px;">Download Launcher</a>
                        <br>
                        <a href="https://euroaion.com/files/directx_jun2008_redist.exe" target="_blank" class="btn btn-link" style="font-size:0.95em;">DirectX June 2008 (Required for some users)</a>
                    </div>
                </div>
            </div>
            <!-- Solution 2: Manual Download (Full Client) -->
            <div class="col-md-6">
                <div class="card text-center shadow-sm" style="border: 1px solid #ff4d4d; border-radius: 10px;">
                    <div class="card-body">
                        <h3 class="colored">Solution 2: Manual Download</h3>
                        <p>
                            <strong>For advanced users or slow connections.</strong><br>
                            1. Download the full client using one of the links below (direct or torrent).<br>
                            2. Extract the archive using <a href="https://www.win-rar.com/download.html" target="_blank">WinRAR</a>.<br>
                            3. Download and place the <b>NeoAion Launcher</b> in the game folder.<br>
                            4. Run the launcher to update and play.
                        </p>
                        <a href="https://neoaion.fr/dl/Neo_Aion_Client 4.6.rar" target="_blank" class="button button-flash" style="margin-bottom:8px;">Download Full Client (Direct machine)</a>
                        <br>
                        <a href="https://neoaion.fr/dl/neoaion_client.torrent" target="_blank" class="button button-flash" style="margin-bottom:8px;" download>Download Torrent</a>
                        <br>
                        <a href="https://www.neoaion.fr/dl/NeoAion_0.1.0_x64-setup.exe" target="_blank" class="button button-flash" style="margin-bottom:8px;">Download Launcher</a>
                        <br>
                        <a href="https://euroaion.com/files/directx_jun2008_redist.exe" target="_blank" class="btn btn-link" style="font-size:0.95em;">DirectX June 2008 (Required for some users)</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="comunity" class="medium-margin">
    <a href="comunity"></a><!-- Nav Anchor -->
    <div class="container">
        <div class="row heading tiny-margin">
            <div class="col-md-auto">
                <h1 class="animation-element slide-down">GAME<span class="colored">COMMUNITY</span></h1>
            </div>
            <div class="col">
                <hr class="animation-element extend">
            </div>
        </div>
        <div class="row medium-margin">
            <div class="col-md-11">
                <h2 class="short-hr-left">JOIN OUR COMMUNITY</h2>
                <p></p><br>
            </div>
            <!-- Community Links -->
            <?php 
            $communityLinks = [
                ['name' => 'FACEBOOK', 'url' => 'https://www.facebook.com/profile.php?id=61574850722056'],
                ['name' => 'DISCORD', 'url' => 'https://discord.gg/kQKmWwcywq'],
                ['name' => 'TIKTOK', 'url' => 'https://www.tiktok.com/@neoaionx?lang=de-DE'],
                ['name' => 'TWITTER', 'url' => 'https://x.com/NeoAion_'],
                ['name' => 'REDDIT', 'url' => 'https://www.reddit.com/user/NeoAion_/'],
                ['name' => 'INSTAGRAM', 'url' => 'https://www.instagram.com/neoaion_/']
            ];
            foreach ($communityLinks as $link) { ?>
                <div class="col-md-4">
                    <div class="card text-center shadow-sm" style="border: 1px solid #ff4d4d; border-radius: 10px;">
                        <div class="card-body">
                            <h3 class="colored"><?php echo $link['name']; ?></h3>
                            <a class="button primary button-flash" href="<?php echo $link['url']; ?>" target="_blank">JOIN NOW</a>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>

        <div id="ranking" class='medium-margin'>
            <a href="ranking"></a><!-- Nav Anchor -->
            <div class="row heading tiny-margin">
                <div class="col-md-auto">
                    <h1 class="animation-element slide-down">GAME<span class="colored">RANKINGS</span></h1>
                </div>
                <div class="col">
                    <hr class="animation-element extend">
                </div>
            </div>
            <div style="display: flex; justify-content: center;">
        <div style="width:80vw; max-width:1400px; min-width:320px;">
            <nav class="nav nav-tabs nav-justified" id="myTab" role="tablist" style="margin-bottom:10px;">
                <a class="nav-item nav-link active" id="notscrool" style="font-size: 14px;" data-toggle="tab" role="tab" aria-controls="rate_1" href="#rate_1">Abyss Ranking</a>
                <a class="nav-item nav-link" id="notscrool" style="font-size: 14px;" data-toggle="tab" role="tab" aria-controls="rate_3" href="#rate_3">Kills Ranking</a>
                <a class="nav-item nav-link" id="notscrool" style="font-size: 14px;" data-toggle="tab" role="tab" aria-controls="rate_11" href="#rate_11">Legion Ranking</a>
            </nav>
            <div class="tab-content" id="myTabContent" style="background: #fff5f5; border: 1px solid #ff4d4d; border-radius: 10px; padding: 10px 0;">
                <div id="rate_1" class="tab-pane show active" role="tabpanel" aria-labelledby="rate_1-tab">
                    <?php
                        $dbs = con::connectionDB('game');
                        $ranking = $dbs->queryFetch("SELECT TOP 20 * FROM user_data ORDER BY abyss_point DESC");
                    ?>
                    <?php if (is_array($ranking)) { ?>
                    <div style="overflow-x:auto;">
                    <table class="table" style="margin:0 auto; width:98%; max-width:1300px; font-size: 14px; text-align:center;">
                        <thead>
                            <tr>
                                <th scope="col" style="width:32px;">#</th>
                                <th scope="col" style="width:80px;">Abyss</th>
                                <th scope="col" style="width:90px;">Name</th>
                                <th scope="col" style="width:60px;">Class</th>
                                <th scope="col" style="width:60px;">Race</th>
                                <th scope="col" style="width:40px;">Lvl</th>
                                <th scope="col" style="width:90px;">Legion</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                                $i = 1;
                                foreach ($ranking as $rank) { 
                                    $legiondata = $dbs->queryFetchSingle("SELECT * FROM guild WHERE id = ?", array($rank['guild_id']));
                                    $rowClass = '';
                                    if ($i == 1) $rowClass = 'style="background:#ffd70044;font-weight:bold;font-size:16px;"';
                                    elseif ($i == 2) $rowClass = 'style="background:#c0c0c044;font-weight:bold;"';
                                    elseif ($i == 3) $rowClass = 'style="background:#cd7f3244;font-weight:bold;"';
                                    else $rowClass = '';
                                    echo "<tr $rowClass>";
                                        echo '<td>'.$i.'</td>';
                                        echo '<td>'.number_format($rank['abyss_point']).' AP</td>';
                                        echo '<td>'.$rank['user_id'].'</td>';
                                        echo '<td>'.numCLassToImg($rank['class']).'</td>';
                                        echo '<td>'.numRaceImg($rank['race']).'</td>';
                                        echo '<td>'.$rank['lev'].'</td>';
                                        if(is_array($legiondata)) {
                                            echo '<td>'.$legiondata['name'].'</td>';
                                        } else {
                                            echo '<td></td>';
                                        }
                                    echo '</tr>';
                                    $i++;
                                }
                            ?>
                        </tbody>
                    </table>
                    </div>
                    <?php } else { ?>
                        <br>
                        <?php message('There have no ranking.', 'warning') ?>
                    <?php } ?>
                </div>
                <div id="rate_3" class="tab-pane" role="tabpanel" aria-labelledby="rate_3-tab">
                    <?php
                        $dbs = con::connectionDB('game');
                        $ranking = $dbs->queryFetch("SELECT TOP 20 * FROM user_data ORDER BY total_abyss_kill_cnt DESC");
                    ?>
                    <?php if (is_array($ranking)) { ?>
                    <div style="overflow-x:auto;">
                    <table class="table" style="margin:0 auto; width:98%; max-width:1300px; font-size: 14px; text-align:center;">
                        <thead>
                            <tr>
                                <th scope="col" style="width:32px;">#</th>
                                <th scope="col" style="width:80px;">Kills</th>
                                <th scope="col" style="width:90px;">Name</th>
                                <th scope="col" style="width:60px;">Class</th>
                                <th scope="col" style="width:60px;">Race</th>
                                <th scope="col" style="width:40px;">Lvl</th>
                                <th scope="col" style="width:90px;">Legion</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                                $i = 1;
                                foreach ($ranking as $rank) { 
                                    $legiondata = $dbs->queryFetchSingle("SELECT * FROM guild WHERE id = ?", array($rank['guild_id']));
                                    $rowClass = '';
                                    if ($i == 1) $rowClass = 'style="background:#ffd70044;font-weight:bold;font-size:16px;"';
                                    elseif ($i == 2) $rowClass = 'style="background:#c0c0c044;font-weight:bold;"';
                                    elseif ($i == 3) $rowClass = 'style="background:#cd7f3244;font-weight:bold;"';
                                    else $rowClass = '';
                                    echo "<tr $rowClass>";
                                        echo '<td>'.$i.'</td>';
                                        echo '<td>'.number_format($rank['total_abyss_kill_cnt']).' Kills</td>';
                                        echo '<td>'.$rank['user_id'].'</td>';
                                        echo '<td>'.numCLassToImg($rank['class']).'</td>';
                                        echo '<td>'.numRaceImg($rank['race']).'</td>';
                                        echo '<td>'.$rank['lev'].'</td>';
                                        if(is_array($legiondata)) {
                                            echo '<td>'.$legiondata['name'].'</td>';
                                        } else {
                                            echo '<td></td>';
                                        }
                                    echo '</tr>';
                                    $i++;
                                }
                            ?>
                        </tbody>
                    </table>
                    </div>
                    <?php } else { ?>
                        <br>
                        <?php message('There have no ranking.', 'warning') ?>
                    <?php } ?>
                </div>
                <div id="rate_11" class="tab-pane" role="tabpanel" aria-labelledby="rate_11-tab">
                    <?php
                        $dbs = con::connectionDB('game');
                        $ranking = $dbs->queryFetch("SELECT TOP 20 * FROM abyss_region_ranking ORDER BY point DESC");
                    ?>
                    <?php if (is_array($ranking)) { ?>
                    <div style="overflow-x:auto;">
                    <table class="table" style="margin:0 auto; width:98%; max-width:1300px; font-size: 14px; text-align:center;">
                        <thead>
                            <tr>
                                <th scope="col" style="width:32px;">#</th>
                                <th scope="col" style="width:80px;">Points</th>
                                <th scope="col" style="width:120px;">Legion Name</th>
                                <th scope="col" style="width:60px;">Race</th>
                                <th scope="col" style="width:40px;">Lvl</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                                $i = 1;
                                foreach ($ranking as $rank) { 
                                    $rowClass = '';
                                    if ($i == 1) $rowClass = 'style="background:#ffd70044;font-weight:bold;font-size:16px;"';
                                    elseif ($i == 2) $rowClass = 'style="background:#c0c0c044;font-weight:bold;"';
                                    elseif ($i == 3) $rowClass = 'style="background:#cd7f3244;font-weight:bold;"';
                                    else $rowClass = '';
                                    echo "<tr $rowClass>";
                                        echo '<td>'.$i.'</td>';
                                        echo '<td>'.number_format($rank['point']).'</td>';
                                        echo '<td>'.$rank['name'].'</td>';
                                        echo '<td>'.numRaceImg($rank['race']).'</td>';
                                        echo '<td>'.$rank['level'].'</td>';
                                    echo '</tr>';
                                    $i++;
                                }
                            ?>
                        </tbody>
                    </table>
                    </div>
                    <?php } else { ?>
                        <br>
                        <?php message('There have no ranking.', 'warning') ?>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

<!-- Ajout d'animations flash CSS et d'effets JavaScript pour dynamiser la page -->
<style>
/* Flash animation for main titles and important elements */
.flash-anim {
    animation: flashGold 1.2s linear infinite alternate;
}
@keyframes flashGold {
    from { color: #ff4d4d; text-shadow: 0 0 8px #ff4d4d44; }
    to   { color: #fff; text-shadow: 0 0 16px #ff4d4dcc; }
}
/* Pulse animation for buttons */
.button-flash:hover, .btn-flash:hover {
    animation: btnPulse 0.4s;
}
@keyframes btnPulse {
    0% { box-shadow: 0 0 0 0 #ff4d4d44; }
    70% { box-shadow: 0 0 12px 8px #ff4d4d44; }
    100% { box-shadow: 0 0 0 0 #ff4d4d44; }
}
/* Card hover effect */
.job-card.flash-card, .card.news-card.flash-card {
    transition: box-shadow 0.2s, transform 0.2s;
}
.job-card.flash-card:hover, .card.news-card.flash-card:hover {
    box-shadow: 0 4px 24px #ff4d4d55;
    transform: translateY(-4px) scale(1.03);
}
.animated-info-card {
    opacity: 0;
    transform: translateY(40px) scale(0.96);
    animation: infoCardIn 0.7s cubic-bezier(.23,1.01,.32,1) forwards;
    animation-delay: 0.2s;
    margin-bottom: 18px;
    transition: box-shadow 0.2s, transform 0.2s;
}
.animated-info-card:hover {
    box-shadow: 0 8px 32px #ff4d4d44;
    transform: translateY(-4px) scale(1.03);
}
.animated-info-card:nth-child(1) { animation-delay: 0.1s; }
.animated-info-card:nth-child(2) { animation-delay: 0.2s; }
.animated-info-card:nth-child(3) { animation-delay: 0.3s; }
.animated-info-card:nth-child(4) { animation-delay: 0.4s; }
.animated-info-card:nth-child(5) { animation-delay: 0.5s; }
.animated-info-card:nth-child(6) { animation-delay: 0.6s; }
.animated-info-card:nth-child(7) { animation-delay: 0.7s; }
.animated-info-card:nth-child(8) { animation-delay: 0.8s; }
.animated-info-card:nth-child(9) { animation-delay: 0.9s; }
@keyframes infoCardIn {
    from { opacity: 0; transform: translateY(40px) scale(0.96);}
    to   { opacity: 1; transform: translateY(0) scale(1);}
}
</style>
<script>
// Optionally, you can trigger the animation on scroll for better performance
document.addEventListener('DOMContentLoaded', function() {
    var cards = document.querySelectorAll('.animated-info-card');
    function animateCards() {
        cards.forEach(function(card) {
            var rect = card.getBoundingClientRect();
            if(rect.top < window.innerHeight - 40) {
                card.style.animationPlayState = 'running';
            }
        });
    }
    animateCards();
    window.addEventListener('scroll', animateCards);
});
</script>