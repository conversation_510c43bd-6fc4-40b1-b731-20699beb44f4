<?php
function check(...$args) {
    foreach ($args as $a) {
        if (!isset($a) || $a === '' ) return false;
    }
    return true;
}

function scrCharFilter($text) {
    $dangerous = ["\\", "'", ",", ";", "--", "-", "%20", "%27", " ", "`", "=", "%", '"'];
    return str_replace($dangerous, '', $text);
}

function encrypte($str) {
    // Same algorithm used by the original site (returns hex string with 0x prefix)
    $key = [];$dst = [];$i = 0;$nBytes = strlen($str);
    // Initialize to 0 to avoid undefined offsets when password length < 16
    for ($i = 1; $i <= 16; $i++) { $key[$i] = 0; $dst[$i] = 0; }
    $i = 0;
    while ($i < $nBytes && $i < 16) { $i++; $key[$i] = ord(substr($str, $i-1, 1)); $dst[$i] = $key[$i]; }
    $rslt = $key[1]+$key[2]*256+$key[3]*65536+$key[4]*16777216; $one = $rslt*213119+2529077; $one = $one - intval($one/4294967296)*4294967296;
    $rslt = $key[5]+$key[6]*256+$key[7]*65536+$key[8]*16777216; $two = $rslt*213247+2529089; $two = $two - intval($two/4294967296)*4294967296;
    $rslt = $key[9]+$key[10]*256+$key[11]*65536+$key[12]*16777216; $three = $rslt*213203+2529589; $three = $three - intval($three/4294967296)*4294967296;
    $rslt = $key[13]+$key[14]*256+$key[15]*65536+$key[16]*16777216; $four = $rslt*213821+2529997; $four = $four - intval($four/4294967296)*4294967296;
    $key[4]=intval($one/16777216); $key[3]=intval(($one-$key[4]*16777216)/65535); $key[2]=intval(($one-$key[4]*16777216-$key[3]*65536)/256); $key[1]=intval(($one-$key[4]*16777216-$key[3]*65536-$key[2]*256));
    $key[8]=intval($two/16777216); $key[7]=intval(($two-$key[8]*16777216)/65535); $key[6]=intval(($two-$key[8]*16777216-$key[7]*65536)/256); $key[5]=intval(($two-$key[8]*16777216-$key[7]*65536-$key[6]*256));
    $key[12]=intval($three/16777216); $key[11]=intval(($three-$key[12]*16777216)/65535); $key[10]=intval(($three-$key[12]*16777216-$key[11]*65536)/256); $key[9]=intval(($three-$key[12]*16777216-$key[11]*65536-$key[10]*256));
    $key[16]=intval($four/16777216); $key[15]=intval(($four-$key[16]*16777216)/65535); $key[14]=intval(($four-$key[16]*16777216-$key[15]*65536)/256); $key[13]=intval(($four-$key[16]*16777216-$key[15]*65536-$key[14]*256));
    $dst[1] = $dst[1] ^ $key[1]; $i=1; while($i<16){$i++; $dst[$i] = $dst[$i] ^ $dst[$i-1] ^ $key[$i];}
    $i=0; while($i<16){$i++; if($dst[$i]==0){$dst[$i]=102;}}
    $encrypt = '0x'; $i=0; while($i<16){$i++; $encrypt .= ($dst[$i]<16 ? '0'.dechex($dst[$i]) : dechex($dst[$i]));}
    return $encrypt;
}

function load_config() { static $c=null; if($c===null){ $c = require __DIR__.'/config.php'; } return $c; }
function db_ls() {
    $c = load_config();
    return new Database($c['SQLSERVER_LS'],$c['SQLUSER_LS'],$c['SQLPASSWORD_LS'],$c['SQLDATABASE_LS'],$c['SQLPORT_LS']);
}
function db_web() {
    $c = load_config();
    return new Database($c['SQLSERVER_WEB'],$c['SQLUSER_WEB'],$c['SQLPASSWORD_WEB'],$c['SQLDATABASE_WEB'],$c['SQLPORT_WEB']);
}
function db_gs() {
    $c = load_config();
    return new Database($c['SQLSERVER_GS'],$c['SQLUSER_GS'],$c['SQLPASSWORD_GS'],$c['SQLDATABASE_GS'],$c['SQLPORT_GS']);
}

function start_session() {
    if (session_status() !== PHP_SESSION_ACTIVE) {
        if (PHP_VERSION_ID >= 70300) {
            // Ensure cookie is valid across the whole site and survives redirects
            session_set_cookie_params([
                'lifetime' => 0,
                'path' => '/',
                'httponly' => true,
                'samesite' => 'Lax',
            ]);
        } else {
            session_set_cookie_params(0, '/');
        }
        session_start();
    }
}

function isLoggedIn() { start_session(); return !empty($_SESSION['valid']) && !empty($_SESSION['uid']); }
function newSession($uid,$account,$email='') { start_session(); $_SESSION['valid']=true; $_SESSION['uid']=$uid; $_SESSION['username']=$account; $_SESSION['email']=$email; }
function logoutSession() {
    start_session();
    // Clear all session variables
    $_SESSION = [];
    // Delete the session cookie
    if (ini_get('session.use_cookies')) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000, $params['path'] ?: '/', $params['domain'] ?? '', $params['secure'] ?? false, $params['httponly'] ?? true);
    }
    // Destroy the session
    session_destroy();
}

